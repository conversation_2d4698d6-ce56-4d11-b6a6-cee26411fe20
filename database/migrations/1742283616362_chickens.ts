import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'chickens'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('token_id').notNullable()
      table.integer('chicken_left_token_id')
      table.integer('chicken_right_token_id')
      table.string('balance').defaultTo('0')
      table.json('ninuno')
      table.string('genes')
      table.integer('is_hatched').defaultTo(1)
      table.timestamp('hatched_at')
      table.integer('generation').defaultTo(0)
      table.integer('parent_breeding_state_time').defaultTo(0)
      table.integer('hatched_time').defaultTo(0)

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
