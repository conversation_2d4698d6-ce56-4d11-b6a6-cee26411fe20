import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class extends BaseSchema {
  protected tableName = 'breed_events'

  public async up() {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.string('address')
      table.string('block_hash')
      table.bigInteger('block_number')
      table.text('data')
      table.integer('log_index')
      table.string('transaction_hash')
      table.integer('transaction_index')
      table.boolean('removed')
      table.json('args')
      table.string('event_name')
      table.integer('processed')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })
    })
  }

  public async down() {
    this.schema.dropTable(this.tableName)
  }
}
