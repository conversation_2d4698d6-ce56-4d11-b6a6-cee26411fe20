import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import { getLatestAssetPrices } from 'App/Helper/asset-prices'
import BreedingFee from 'App/Models/BreedingFee'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method

    const { cockPrice } = await getLatestAssetPrices()

    if (!cockPrice) {
      throw new Error('Failed to fetch $COCK price')
    }

    const BREEDING_FEES = [
      {
        count: 0,
        cockUsd: 2.5,
        feathers: 30,
      },
      {
        count: 1,
        cockUsd: 5,
        feathers: 45,
      },
      {
        count: 2,
        cockUsd: 9,
        feathers: 60,
      },
      {
        count: 3,
        cockUsd: 15,
        feathers: 90,
      },
      {
        count: 4,
        cockUsd: 25,
        feathers: 120,
      },
      {
        count: 5,
        cockUsd: 40,
        feathers: 150,
      },
      {
        count: 6,
        cockUsd: 60,
        feathers: 180,
      },
      {
        count: 7,
        cockUsd: 80,
        feathers: 200,
      },
    ].map((fee) => ({
      ...fee,
      cock: fee.cockUsd / cockPrice,
    }))

    await BreedingFee.createMany(BREEDING_FEES)
  }
}
