import Application from '@ioc:Adonis/Core/Application'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Chicken, { EChickenIsHatchedStatus } from 'App/Models/Chicken'
import fs from 'fs'

const basePath = Application.appRoot

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    // create chickens upto 11110
    const totalNinunoChickens = 11110

    for (let i = 1; i <= totalNinunoChickens; i++) {
      let rawdata = fs.readFileSync(`${basePath}/metadata/${i}.json`)
      let data = JSON.parse(rawdata.toString())

      const geneAttribute = data.attributes.find((attr) => attr.trait_type === 'Genes')

      if (!geneAttribute) {
        throw new Error(`Failed to find gene attribute for chicken ${i}`)
      }

      const genes = geneAttribute.value.toString()

      await Chicken.create({
        id: i,
        tokenId: i,
        genes: genes,
        ninuno: [],
        isHatched: EChickenIsHatchedStatus.YES,
        balance: 0n,
      })

      console.log(`Created chicken ${i}`)
    }
  }
}
