import Application from '@ioc:Adonis/Core/Application'
import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import Chicken from 'App/Models/Chicken'
import fs from 'fs'

const buildDir = `${Application.tmpPath()}/build/json`

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const startTokenId = 11111
    const endTokenId = 12487

    let ctr = 0

    for (let i = startTokenId; i <= endTokenId; i++) {
      let rawdata = fs.readFileSync(`${buildDir}/${i}.json`)
      let data = JSON.parse(rawdata.toString())

      const geneAttribute = data.attributes.find((attr) => attr.trait_type === 'Genes')

      if (!geneAttribute) {
        console.log(i)
        ctr++
        // throw new Error(`Failed to find gene attribute for chicken ${i}`)
      }

      // const genes = geneAttribute.value.toString()

      // await Chicken.query().where('tokenId', i).update({
      //   genes: genes,
      // })

      // console.log(`Updated genes for chicken ${i}`)
    }

    console.log(ctr)
  }
}
