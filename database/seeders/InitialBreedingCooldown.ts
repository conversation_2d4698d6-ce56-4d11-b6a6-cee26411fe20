import BaseSeeder from '@ioc:Adonis/Lucid/Seeder'
import BreedingCooldown from 'App/Models/BreedingCooldown'

export default class extends BaseSeeder {
  public async run() {
    // Write your database queries inside the run method
    const BREEDING_COOLDOWN = [
      {
        count: 0, //breeding count
        cooldown: 1, //cooldown duration in days
      },
      {
        count: 1,
        cooldown: 2,
      },
      {
        count: 2,
        cooldown: 3,
      },
      {
        count: 3,
        cooldown: 5,
      },
      {
        count: 4,
        cooldown: 8,
      },
      {
        count: 5,
        cooldown: 12,
      },
      {
        count: 6,
        cooldown: 18,
      },
      {
        count: 7,
        cooldown: 25,
      },
    ]

    await BreedingCooldown.createMany(BREEDING_COOLDOWN)
  }
}
