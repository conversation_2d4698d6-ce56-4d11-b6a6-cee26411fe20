import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import SABONG_CONFIG from 'Config/sabong'

export default class Admin {
  public async handle({ auth, response }: HttpContextContract, next: () => Promise<void>) {
    if (
      auth.user &&
      !SABONG_CONFIG.ADMIN_ADDRESSES.includes(auth.user.blockchainAddress.toLowerCase())
    ) {
      response.status(403)
      return response.json({
        status: 0,
        message: 'Permission denied',
      })
    }
    await next()
  }
}
