import BreedingCooldown from 'App/Models/BreedingCooldown'
import BreedingFee from 'App/Models/BreedingFee'
import SABONG_CONFIG from 'Config/sabong'
import fs from 'fs'
import path from 'path'
import Application from '@ioc:Adonis/Core/Application'

// Define a type for valid trait names
type TraitName =
  | 'Beak'
  | 'Body'
  | 'Comb'
  | 'Eyes'
  | 'Feet'
  | 'Tail'
  | 'Wings'
  | 'Color'
  | 'Instinct'

// Define interfaces for our data structures
interface TraitDecoding {
  p: string
  h1: string
  h2: string
  h3: string
}

interface DecodedGene {
  [key: string]: TraitDecoding | string | number
}

// Trait mappings with legendary traits marked
const traitMappings = {
  Beak: [
    'Chim Lạc',
    '<PERSON>bird',
    '<PERSON><PERSON>',
    '<PERSON>rimano<PERSON>', // Legendary traits (0-3)
    'Wormtongue',
    '<PERSON>',
    'Ironclad',
    'Piercing Fang',
    '<PERSON><PERSON>',
    'Nightwave',
    'Touca',
    'Blade Spire',
    '<PERSON>',
    '<PERSON>erd<PERSON>',
    '<PERSON><PERSON>',
    '<PERSON><PERSON>',
    'Radian<PERSON>',
    '<PERSON>lare',
    '<PERSON><PERSON>',
    '<PERSON>bla<PERSON>',
  ],
  Body: ['<PERSON><PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>iest', 'Wickid', 'Jordi'],
  Comb: [
    'Minokawa',
    'Adarna',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
    'Suave',
    'Single',
    'Corona',
    'Goodboy',
    'Sasuke',
    'Cubao',
    'Igop',
    'Hellboy',
    'Spike',
    'Raditz',
    'Super Sayang 4',
    'Power Geyser',
    'Yugi',
    'Super Sayang 1',
    'Killua',
    'Sakuragi',
  ],
  Eyes: [
    'Garuda',
    'Minokawa',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
    'Batak',
    'Bagyo',
    'Shookt',
    'Dyosa',
    'Silog',
    'Santelmo',
    'Tuko',
    'Peyups',
    'Agila',
    'Atenista',
    'Lasallista',
    'Wildfire',
    'Diwata',
    'Maxx',
    'Retokada',
    'Yinyang',
  ],
  Feet: [
    'Buakaw',
    'Alicanto Oro',
    'Alicanto Plata',
    'Thunderbird', // Legendary traits (0-3)
    'Mahiwaga',
    'Luntian',
    'Makopa',
    'Sibat',
    'Cemani',
    'Pula',
    'Zenki',
    'Ember',
    'Hepa Lane',
    'Kaliskis',
    'Mewling Tiger',
    'Dionela',
    'Onyx',
    'Chernobyl',
    'Paleclaws',
    'Catriona',
  ],
  Tail: [
    'Simurgh',
    'Chim Lạc',
    'Minokawa',
    'Adarna', // Legendary traits (0-3)
    'Agave',
    'Rengoku',
    'Starjeatl',
    'Carota',
    'Abaniko',
    'Onagadori',
  ],
  Wings: [
    'Adarna',
    'Minokawa',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
    'Mandingo',
    'Helena',
    'Potsu',
    'Johny',
    'Slenduh',
    'Awra',
  ],
  Color: [
    'A1',
    'A2',
    'A3',
    'B1',
    'B2',
    'B3',
    'C1',
    'C2',
    'C3',
    'D1',
    'D2',
    'D3',
    'E1',
    'E2',
    'E3',
    'F1',
    'F2',
    'F3',
    'G1',
    'G2',
    'G3',
    'H1',
    'H2',
    'H3',
  ],
  Instinct: [
    'Aggressive',
    'Steadfast',
    'Swift',
    'Stalwart',
    'Balanced',
    'Reckless',
    'Resolute',
    'Elusive',
    'Tenacious',
    'Unyielding',
    'Vicious',
    'Adaptive',
    'Versatile',
    'Relentless',
    'Blazing',
    'Bulwark',
    'Enduring',
  ],
}

// Function to decode a gene segment for composite traits
function decodeCompositeGeneSegment(segment: number, traitName: TraitName): TraitDecoding {
  const traitList = traitMappings[traitName]

  // Extract the indices directly from the segment
  // Format: p (8 bits) + h1 (8 bits) + h2 (8 bits) + h3 (8 bits) = 32 bits
  const p = (segment >> 24) & 0xff
  const h1 = (segment >> 16) & 0xff
  const h2 = (segment >> 8) & 0xff
  const h3 = segment & 0xff

  return {
    p: p < traitList.length ? traitList[p] : 'Unknown',
    h1: h1 < traitList.length ? traitList[h1] : 'Unknown',
    h2: h2 < traitList.length ? traitList[h2] : 'Unknown',
    h3: h3 < traitList.length ? traitList[h3] : 'Unknown',
  }
}

// Function to decode a gene segment for simple traits
function decodeSimpleGeneSegment(segment: number, traitName: TraitName): string {
  // For simple traits, we just stored the index directly
  if (traitName === 'Color' || traitName === 'Instinct') {
    const traitList = traitMappings[traitName]
    return segment < traitList.length ? traitList[segment] : 'Unknown'
  }

  // For numeric stats, just return the value
  return String(segment)
}

// Function to count legendary traits
function countLegendaryTraits(decodedGene: DecodedGene): number {
  let legendaryCount = 0

  // List of traits that can be legendary
  const traitsToCheck = ['Beak', 'Comb', 'Eyes', 'Feet', 'Tail', 'Wings']

  for (const trait of traitsToCheck) {
    const traitValue = decodedGene[trait]
    if (typeof traitValue === 'object' && 'p' in traitValue) {
      // Check if the dominant trait (p) is legendary (index 0-3)
      const traitList = traitMappings[trait as keyof typeof traitMappings]
      const index = traitList.indexOf(traitValue.p)
      if (index >= 0 && index <= 3) {
        legendaryCount++
      }
    }
  }

  return legendaryCount
}

// Function to decode a gene
export function decodeGene(geneHex: string): DecodedGene | null {
  try {
    // Convert hex to binary
    const geneBigInt = BigInt(`0x${geneHex}`)
    const geneBinary = geneBigInt.toString(2).padStart(512, '0') // 16 segments * 32 bits

    // Split into 32-bit segments
    const segments: number[] = []
    for (let i = 0; i < geneBinary.length; i += 32) {
      const segment = geneBinary.substring(i, i + 32)
      segments.push(parseInt(segment, 2))
    }

    // Define the trait order in the gene
    const traitOrder: Array<{ name: string; isComposite: boolean }> = [
      { name: 'Feet', isComposite: true },
      { name: 'Tail', isComposite: true },
      { name: 'Body', isComposite: true },
      { name: 'Wings', isComposite: true },
      { name: 'Eyes', isComposite: true },
      { name: 'Beak', isComposite: true },
      { name: 'Comb', isComposite: true },
      { name: 'Color', isComposite: true },
      { name: 'Innate Attack', isComposite: false },
      { name: 'Innate Defense', isComposite: false },
      { name: 'Innate Speed', isComposite: false },
      { name: 'Innate Health', isComposite: false },
      { name: 'Instinct', isComposite: false },
    ]

    // Decode each segment
    const decodedTraits: DecodedGene = {}

    for (let i = 0; i < Math.min(segments.length, traitOrder.length); i++) {
      const segment = segments[i]
      const { name, isComposite } = traitOrder[i]

      if (
        isComposite &&
        (name === 'Feet' ||
          name === 'Tail' ||
          name === 'Body' ||
          name === 'Wings' ||
          name === 'Eyes' ||
          name === 'Beak' ||
          name === 'Comb' ||
          name === 'Color')
      ) {
        decodedTraits[name] = decodeCompositeGeneSegment(segment, name as TraitName)
      } else if (!isComposite && name === 'Instinct') {
        decodedTraits[name] = decodeSimpleGeneSegment(segment, name as TraitName)
      } else {
        // For numeric stats
        decodedTraits[name] = segment
      }
    }

    const legendaryCount = countLegendaryTraits(decodedTraits)

    // Add legendary count to the decoded traits
    decodedTraits['LegendaryCount'] = legendaryCount

    return decodedTraits
  } catch (error) {
    console.error('Error decoding gene:', error)
    return null
  }
}

// Function to recalcute breeding fee upon reaching threshold
export function recalculateBreedingFee(
  breedingCount: number,
  breedingFee: BreedingFee
): { cock: number; feathers: number } {
  const conversionRate = breedingFee.cockUsd / breedingFee.cock

  if (breedingCount > SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE) {
    // Calculate how many times over the threshold
    const excessCount = breedingCount - SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE

    // Convert $20 to COCK tokens using the conversion rate
    const additionalCockPerExcess = 20 / conversionRate

    // Add the additional COCK tokens for each excess count
    const totalCock = breedingFee.cock + additionalCockPerExcess * excessCount
    // Add 20 FEATHERS for each excess count
    const totalFeathers = breedingFee.feathers + 20 * excessCount

    return { cock: totalCock, feathers: totalFeathers }
  } else {
    return { cock: breedingFee.cock, feathers: breedingFee.feathers }
  }
}

// Function to recalcute breeding cooldown upon reaching threshold
export function recalculateBreedingCooldown(
  breedingCount: number,
  breedingCooldown: BreedingCooldown
): { cooldown: number } {
  if (breedingCount > SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION) {
    const excessCount = breedingCount - SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION

    return { cooldown: breedingCooldown.cooldown + 5 * excessCount }
  } else {
    return { cooldown: breedingCooldown.cooldown }
  }
}

// REROLL FUNCTIONS
const generateRandomIPTraits = () => {
  const maxTotalIP = 160 // Maximum total IP
  const minTotalIP = 0 // Minimum total IP
  const maxStat = 40 // Maximum value per stat
  const minStat = 0 // Minimum value per stat

  // First, randomly determine total IP to distribute
  const totalIPToDistribute = Math.floor(Math.random() * (maxTotalIP - minTotalIP + 1)) + minTotalIP

  const ipTraits = [
    { trait_type: 'Innate Attack', value: 0, display_type: 'number' },
    { trait_type: 'Innate Defense', value: 0, display_type: 'number' },
    { trait_type: 'Innate Speed', value: 0, display_type: 'number' },
    { trait_type: 'Innate Health', value: 0, display_type: 'number' },
  ]

  // First, assign minimum values
  let remainingIP = totalIPToDistribute - minStat * ipTraits.length
  ipTraits.forEach((trait) => {
    trait.value = minStat
  })

  // Create random weights for distribution
  const weights = ipTraits.map(() => Math.random())
  const totalWeight = weights.reduce((a, b) => a + b, 0)

  // Distribute remaining points proportionally based on weights
  ipTraits.forEach((trait, index) => {
    const share = Math.floor((weights[index] / totalWeight) * remainingIP)
    const potentialValue = trait.value + share

    // Ensure we don't exceed maxStat
    trait.value = Math.min(potentialValue, maxStat)
    remainingIP -= trait.value - minStat
  })

  // If there are still points remaining, distribute them randomly
  while (remainingIP > 0) {
    const eligibleTraits = ipTraits.filter((trait) => trait.value < maxStat)
    if (eligibleTraits.length === 0) break

    const randomTrait = eligibleTraits[Math.floor(Math.random() * eligibleTraits.length)]
    const increment = Math.min(remainingIP, maxStat - randomTrait.value)

    randomTrait.value += increment
    remainingIP -= increment
  }

  // Shuffle the traits to avoid any pattern in the distribution
  for (let i = ipTraits.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1))
    ;[ipTraits[i], ipTraits[j]] = [ipTraits[j], ipTraits[i]]
  }

  return ipTraits
}

// Function to get a random non-legendary trait index
function getRandomNonLegendaryIndex(traitName) {
  const traitList = traitMappings[traitName]
  const legendaryCount = traitName === 'Body' || traitName === 'Color' ? 0 : 4 // Body has no legendary traits

  // Return a random index from the non-legendary range
  return legendaryCount + Math.floor(Math.random() * (traitList.length - legendaryCount))
}

// SIMPLIFIED: Function to encode a trait into a gene segment
function encodeTraitToGene(traitName, traitValue, isComposite, offspringTraits) {
  if (isComposite) {
    // Check if traitName is a valid composite trait
    if (
      traitName in traitMappings &&
      ['Beak', 'Body', 'Comb', 'Eyes', 'Feet', 'Tail', 'Wings', 'Color'].includes(traitName)
    ) {
      const traitList = traitMappings[traitName]
      const p = traitList.indexOf(traitValue)

      if (p === -1) {
        console.warn(
          `Warning: Trait value "${traitValue}" not found in ${traitName} list. Using default index 0.`
        )
        return 0
      }

      // Generate random recessive traits (never legendary, but can be the same)
      let h1
      let h2
      let h3

      // For h1, h2, h3 - always use non-legendary traits
      if (offspringTraits && offspringTraits[traitName]) {
        h1 = traitList.indexOf(offspringTraits[traitName].h1)
        h2 = traitList.indexOf(offspringTraits[traitName].h2)
        h3 = traitList.indexOf(offspringTraits[traitName].h3)
      } else {
        h1 = getRandomNonLegendaryIndex(traitName)
        h2 = getRandomNonLegendaryIndex(traitName)
        h3 = getRandomNonLegendaryIndex(traitName)
      }

      // SIMPLIFIED ENCODING: Just store the indices directly
      // Format: p (8 bits) + h1 (8 bits) + h2 (8 bits) + h3 (8 bits) = 32 bits
      // We'll only use the lower 8 bits of each 16-bit segment
      return (p << 24) | (h1 << 16) | (h2 << 8) | h3
    }
    return 0 // Default value for unknown traits
  } else {
    // For simple traits, just store the index
    if (traitName === 'Instinct' && traitName in traitMappings) {
      const instinctIndex = traitMappings.Instinct.indexOf(traitValue)
      return instinctIndex >= 0 ? instinctIndex : 0
    } else if (
      traitName.includes('Attack') ||
      traitName.includes('Defense') ||
      traitName.includes('Speed') ||
      traitName.includes('Health')
    ) {
      // For numeric stats, just use the value directly
      const numValue = parseInt(traitValue)
      return isNaN(numValue) ? 0 : numValue
    }
  }
  return 0
}

// Function to generate a gene from traits
function generateBaseGene(traits, offspringTraits = {}) {
  const traitTemplate = [
    { trait: 'Feet', isComposite: true },
    { trait: 'Tail', isComposite: true },
    { trait: 'Body', isComposite: true },
    { trait: 'Wings', isComposite: true },
    { trait: 'Eyes', isComposite: true },
    { trait: 'Beak', isComposite: true },
    { trait: 'Comb', isComposite: true },
    { trait: 'Color', isComposite: true },
    { trait: 'Innate Attack', isComposite: false },
    { trait: 'Innate Defense', isComposite: false },
    { trait: 'Innate Speed', isComposite: false },
    { trait: 'Innate Health', isComposite: false },
    { trait: 'Instinct', isComposite: false },
  ]

  // Generate segments for each trait
  const geneSegments = [] as number[]

  for (const template of traitTemplate) {
    const traitName = template.trait
    const traitValue = traits[traitName]

    if (traitValue !== undefined) {
      const geneSegment = encodeTraitToGene(
        traitName,
        String(traitValue),
        template.isComposite,
        offspringTraits
      )
      geneSegments.push(geneSegment)
    } else {
      // If trait is missing, use a placeholder
      geneSegments.push(0)
    }
  }

  // Fill remaining segments with zeros to make 16 segments total
  while (geneSegments.length < 16) {
    geneSegments.push(0)
  }

  // Convert segments to binary, then to hex
  let binaryString = ''
  for (const segment of geneSegments) {
    // Convert each segment to a 32-bit binary string
    binaryString += segment.toString(2).padStart(32, '0')
  }

  // Use BigInt for large binary numbers
  const geneHex = BigInt('0b' + binaryString)
    .toString(16)
    .padStart(64, '0')
  return geneHex
}

export function rerollInnatePoints(tokenId: number) {
  const jsonDir = `${Application.tmpPath()}/build/json`

  // Write sample metadata to file
  const jsonPath = path.join(jsonDir, `${tokenId}.json`)

  // Read the original metadata
  const originalMetadata = JSON.parse(fs.readFileSync(jsonPath, 'utf8'))

  const originalGenes = originalMetadata.attributes.find((attr) => attr.trait_type === 'Genes')
    ?.value

  if (!originalGenes) {
    throw new Error('Failed to find genes in metadata')
  }

  const offspringTraits = decodeGene(originalGenes)

  if (!offspringTraits) {
    throw new Error('Failed to decode genes')
  }

  const newInnateTraits = generateRandomIPTraits()

  const traitObj = {}

  Object.entries(offspringTraits).map(([trait, values]) => {
    traitObj[trait] = typeof values === 'string' || typeof values === 'number' ? values : values.p
  })

  newInnateTraits.forEach((trait) => {
    traitObj[trait.trait_type] = trait.value
  })

  const newGeneratedGenes = [
    ...newInnateTraits,
    {
      trait_type: 'Genes',
      value: generateBaseGene(traitObj, offspringTraits),
      display_type: 'string',
    },
  ]

  // Update the metadata attributes with new innate points
  originalMetadata.attributes = originalMetadata.attributes.map((attr) => {
    const newInnate = newGeneratedGenes.find((t) => t.trait_type === attr.trait_type)
    if (newInnate) {
      return {
        ...attr,
        value: newInnate.value,
      }
    }
    return attr
  })

  // Write the updated metadata back to the file
  fs.writeFileSync(jsonPath, JSON.stringify(originalMetadata, null, 2))
}
