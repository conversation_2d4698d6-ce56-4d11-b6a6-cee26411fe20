import axios from 'axios'

export const getLatestAssetPrices = async () => {
  try {
    const cockTokenAddress = '0x8fd6b3fa81adf438feeb857e0b8aed5f74f718ad'

    const cockPriceData = await axios
      .get(`https://exchange-rate.skymavis.com/v2/prices?addresses=${cockTokenAddress}`)
      .then((response) => response.data)

    if (!cockPriceData.result?.[cockTokenAddress]?.usd) {
      throw new Error(`Failed to fetch COCK price`)
    }

    return {
      cockPrice: (cockPriceData.result?.[cockTokenAddress]?.usd as number) || null,
    }
  } catch (error) {
    // TODO: maybe alert telegram bot on failure of fetching price
    return {
      cockPrice: null,
    }
  }
}
