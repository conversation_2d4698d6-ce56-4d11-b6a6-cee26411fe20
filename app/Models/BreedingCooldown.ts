import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class BreedingCooldown extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public count: number

  @column()
  public cooldown: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
