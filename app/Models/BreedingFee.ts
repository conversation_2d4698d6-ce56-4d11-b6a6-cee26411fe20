import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class BreedingFee extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public count: number

  @column()
  public cock: number

  @column()
  public cockUsd: number

  @column()
  public feathers: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
