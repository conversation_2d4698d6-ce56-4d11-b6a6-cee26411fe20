import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export enum ClaimNinunoRewardProcessedStatus {
  PENDING = 0,
  PROCESSED = 1,
}

export default class ClaimNinunoReward extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public address: string

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public amount: bigint

  @column()
  public processed: ClaimNinunoRewardProcessedStatus

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
