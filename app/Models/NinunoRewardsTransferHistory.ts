import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class NinunoRewardsTransferHistory extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public userId: number

  @column()
  public address: string

  @column()
  public chickenTokenId: number

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public amount: bigint

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
