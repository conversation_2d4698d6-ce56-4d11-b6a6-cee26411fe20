import { DateTime } from 'luxon'
import { BaseModel, belongsTo, BelongsTo, column } from '@ioc:Adonis/Lucid/Orm'

export enum EChickenIsHatchedStatus {
  NO = 0,
  YES = 1,
}

export default class Chicken extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public tokenId: number

  @column()
  public chickenLeftTokenId: number

  @column()
  public chickenRightTokenId: number

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public balance: bigint

  @column({
    prepare: (value) => JSON.stringify(value),
    consume: (value: string) => {
      return value && typeof value === 'string' ? JSON.parse(value) : value
    },
  })
  public ninuno: number[]

  @column()
  public genes: string

  @column()
  public isHatched: EChickenIsHatchedStatus

  @column()
  public parentBreedingStateTime: number // parent breeding cooldown date in seconds

  @column()
  public hatchedTime: number // 7 days in seconds

  @column.dateTime()
  public hatchedAt: DateTime

  @column()
  public generation: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @belongsTo(() => Chicken, { foreignKey: 'chickenLeftTokenId' })
  public chickenLeft: BelongsTo<typeof Chicken>

  @belongsTo(() => Chicken, { foreignKey: 'chickenRightTokenId' })
  public chickenRight: BelongsTo<typeof Chicken>
}
