import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import { DateTime } from 'luxon'

export default class User extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public blockchainAddress: string

  @column()
  public nonce: number

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public claimableBalance: bigint

  @column({
    prepare: (value) => value.toString(),
    consume: (value: string) => {
      return value && typeof value === 'string' ? BigInt(value) : value
    },
  })
  public claimedBalance: bigint

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime
}
