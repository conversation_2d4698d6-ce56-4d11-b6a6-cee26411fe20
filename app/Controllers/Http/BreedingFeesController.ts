import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { getLatestAssetPrices } from 'App/Helper/asset-prices'
import BreedingFee from 'App/Models/BreedingFee'
import CreateBreedingFeeValidator from 'App/Validators/CreateBreedingFeeValidator'
import UpdateBreedingFeeValidator from 'App/Validators/UpdateBreedingFeeValidator'

export default class BreedingFeesController {
  public async update({ request, response }: HttpContextContract) {
    const { breedingFeeId, cockUsd, feathers } = await request.validate(UpdateBreedingFeeValidator)

    const { cockPrice } = await getLatestAssetPrices()

    if (!cockPrice) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to fetch $COCK price',
      })
    }

    const breedingFee = await BreedingFee.find(breedingFeeId)

    if (!breedingFee) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Breeding fee not found',
      })
    }

    if (cockUsd || cockUsd === 0) {
      breedingFee.cockUsd = cockUsd
      breedingFee.cock = cockUsd / cockPrice
    }

    if (feathers || feathers === 0) {
      breedingFee.feathers = feathers
    }

    await breedingFee.save()

    return response.json({
      status: 1,
      message: 'Breeding fees updated',
    })
  }

  public async create({ request, response }: HttpContextContract) {
    const { count, cockUsd, feathers } = await request.validate(CreateBreedingFeeValidator)

    const { cockPrice } = await getLatestAssetPrices()

    if (!cockPrice) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Failed to fetch $COCK price',
      })
    }

    const findExistingCount = await BreedingFee.query().where('count', count).first()

    if (findExistingCount) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Count already exists',
      })
    }

    await BreedingFee.create({
      count,
      cockUsd,
      cock: cockUsd / cockPrice,
      feathers,
    })

    return response.json({
      status: 1,
      message: 'Breeding fees created',
    })
  }

  public async delete({ request, response }: HttpContextContract) {
    const { breedingFeeId } = request.body()

    const breedingFee = await BreedingFee.find(breedingFeeId)

    if (!breedingFee) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Breeding fee not found',
      })
    }

    await breedingFee.delete()

    return response.json({
      status: 1,
      message: 'Breeding fees deleted',
    })
  }

  public async viewAll({ response }: HttpContextContract) {
    const breedingFees = await BreedingFee.query().orderBy('count', 'asc')

    return response.json({
      status: 1,
      data: breedingFees,
    })
  }
}
