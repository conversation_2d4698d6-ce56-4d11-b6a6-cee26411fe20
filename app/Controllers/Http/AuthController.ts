import { recoverMessageAddress } from 'viem/utils'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import User from 'App/Models/User'
import SABONG_CONFIG from 'Config/sabong'

export default class AuthController {
  public async request({ request, response }: HttpContextContract) {
    const { address } = request.body()

    const nonce = Math.floor(Date.now() / 1000)

    const user = await User.firstOrNew(
      {
        blockchainAddress: address,
      },
      {
        blockchainAddress: address,
        nonce: nonce,
      }
    )

    if (user.$isPersisted) {
      user.nonce = nonce
    }

    await user.save()

    const message = `Sabong Saga Login nonce: ${nonce}`

    return response.json({
      status: 1,
      message: 'Waiting for verification...',
      data: message,
    })
  }

  public async verify({ auth, request, response }: HttpContextContract) {
    const { address, signature } = request.body()

    const user = await User.query().where('blockchainAddress', address).first()

    if (!user) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Verification failed. Please reconnect your wallet',
      })
    }

    const message = `Sabong Saga Login nonce: ${user.nonce}`

    const sigAddress = await recoverMessageAddress({
      message: message,
      signature,
    })

    if (sigAddress.toLowerCase() === address.toLowerCase()) {
      const token = await auth.use('api').generate(user)

      return response.json({
        status: 1,
        message: `Welcome back ${user.blockchainAddress}!`,
        token: token.token,
        user,
        isAdmin: SABONG_CONFIG.ADMIN_ADDRESSES.includes(user.blockchainAddress.toLowerCase()),
      })
    }

    response.status(401)
    return response.json({
      status: 0,
      message: 'Invalid signature provided',
    })
  }
}
