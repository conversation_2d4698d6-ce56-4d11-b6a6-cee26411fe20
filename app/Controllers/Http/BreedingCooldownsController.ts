import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import BreedingCooldown from 'App/Models/BreedingCooldown'
import CreateBreedingCooldownValidator from 'App/Validators/CreateBreedingCooldownValidator'
import UpdateBreedingCooldownValidator from 'App/Validators/UpdateBreedingCooldownValidator'

export default class BreedingCooldownsController {
  public async update({ request, response }: HttpContextContract) {
    const { breedingCooldownId, cooldown, count } = await request.validate(
      UpdateBreedingCooldownValidator
    )

    const breedingCooldown = await BreedingCooldown.find(breedingCooldownId)

    if (!breedingCooldown) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Breeding cooldown not found',
      })
    }

    if (cooldown || cooldown === 0) {
      breedingCooldown.cooldown = cooldown
    }

    if (count || count === 0) {
      const findExistingCount = await BreedingCooldown.query().where('count', count).first()

      if (findExistingCount && findExistingCount.id !== breedingCooldownId) {
        response.status(400)
        return response.json({
          status: 0,
          message: 'Count already exists',
        })
      }

      breedingCooldown.count = count
    }

    await breedingCooldown.save()

    return response.json({
      status: 1,
      message: 'Breeding cooldowns updated',
    })
  }

  public async create({ request, response }: HttpContextContract) {
    const { count, cooldown } = await request.validate(CreateBreedingCooldownValidator)

    const findExistingCount = await BreedingCooldown.query().where('count', count).first()

    if (findExistingCount) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Count already exists',
      })
    }

    await BreedingCooldown.create({
      count,
      cooldown,
    })

    return response.json({
      status: 1,
      message: 'Breeding cooldowns created',
    })
  }

  public async delete({ request, response }: HttpContextContract) {
    const { breedingCooldownId } = request.body()

    const breedingCooldown = await BreedingCooldown.find(breedingCooldownId)

    if (!breedingCooldown) {
      response.status(400)
      return response.json({
        status: 0,
        message: 'Breeding cooldown not found',
      })
    }

    await breedingCooldown.delete()

    return response.json({
      status: 1,
      message: 'Breeding cooldowns deleted',
    })
  }

  public async viewAll({ response }: HttpContextContract) {
    const breedingCooldowns = await BreedingCooldown.query().orderBy('count', 'asc')

    return response.json({
      status: 1,
      data: breedingCooldowns,
    })
  }
}
