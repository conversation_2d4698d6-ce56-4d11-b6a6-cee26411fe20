import SABONG_CONFIG from 'Config/sabong'
import { createPublicClient, http } from 'viem'
import { ronin, saigon } from 'viem/chains'
import { BreedEventType, processBreedEvent } from './ProcessBreedEventService'
import BreedEvent, { BreedEventProcessedStatus } from 'App/Models/BreedEvent'
import {
  NinunoBalanceClaimedEventType,
  processNinunoBalanceClaimedEvent,
} from './ProcessNinunoBalanceClaimedEventService'

export default class BreedingEventService {
  public static async watchBreedingEvents() {
    const client = createPublicClient({
      chain: SABONG_CONFIG.ENV === 'production' ? ronin : saigon,
      transport: http(SABONG_CONFIG.RONIN_RPC),
    })

    const getUnProcessedEvents = await BreedEvent.query()
      .where('processed', BreedEventProcessedStatus.PENDING)
      .orderBy('blockNumber', 'asc')
      .first()

    const getLastProcessedEvents = await BreedEvent.query()
      .where('processed', BreedEventProcessedStatus.PROCESSED)
      .orderBy('blockNumber', 'desc')
      .first()

    // Fetch past events
    const latestBlock = await client.getBlockNumber()

    /**
     * Determines the starting block number for event fetching:
     * - If there are unprocessed events, starts from the earliest unprocessed event's block
     * - If there are only processed events, starts from the latest processed event's block
     * - If no events exist, starts from the contract creation block
     * @returns {bigint} The block number to start fetching events from
     */
    const fromBlock = getUnProcessedEvents
      ? getUnProcessedEvents.blockNumber
      : getLastProcessedEvents
      ? getLastProcessedEvents.blockNumber + 1n //skip last success event
      : SABONG_CONFIG.BREEDING_CREATION_BLOCKNUMBER // Creation block

    const toBlock = latestBlock // Get up to the latest block

    console.log(`Fetching past events from block ${fromBlock} to ${toBlock}...`)

    const pastEvents = await client.getContractEvents({
      address: SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS,
      abi: SABONG_CONFIG.ABIS.BREEDING_ABI,
      fromBlock,
      toBlock,
    })

    await processBreedEvent(
      pastEvents.filter((event) => event.eventName === 'Breed' || event.eventName === 'BreedV2')
    )
    await processNinunoBalanceClaimedEvent(
      pastEvents.filter((event) => event.eventName === 'NinunoBalanceClaimed')
    )

    // Watch new events from the next block
    console.log(`Watching new events from block ${toBlock + 1n}...`)

    const unwatch = client.watchContractEvent({
      address: SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS,
      abi: SABONG_CONFIG.ABIS.BREEDING_ABI,
      fromBlock: toBlock + 1n,
      onLogs: async (logs) => {
        try {
          processBreedEvent(
            logs.filter(
              (event) => event.eventName === 'Breed' || event.eventName === 'BreedV2'
            ) as BreedEventType[]
          )
          await processNinunoBalanceClaimedEvent(
            logs.filter(
              (event) => event.eventName === 'NinunoBalanceClaimed'
            ) as NinunoBalanceClaimedEventType[]
          )
        } catch (error) {
          console.log(`Can't process new event: ${error}`)
        }
      },
      onError: (error) => {
        console.log(`Can't watch event: ${error}`)
      },
    })

    return unwatch
  }
}
