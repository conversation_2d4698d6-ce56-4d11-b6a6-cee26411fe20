/* eslint-disable @typescript-eslint/naming-convention */
import Bull from '@ioc:Rocketseat/Bull'
import ProcessNinunoBalanceClaimedEventJob from 'App/Jobs/ProcessNinunoBalanceClaimedEventJob'
import BreedEvent from 'App/Models/BreedEvent'
import ClaimNinunoReward, { ClaimNinunoRewardProcessedStatus } from 'App/Models/ClaimNinunoReward'
import User from 'App/Models/User'

export type NinunoBalanceClaimedEventType = {
  address: `0x${string}`
  blockHash: `0x${string}`
  blockNumber: bigint
  data: `0x${string}`
  logIndex: number
  transactionHash: `0x${string}`
  transactionIndex: number
  removed: boolean
} & {
  args: {
    address?: `0x${string}` | undefined
    withdrawalRequestId?: bigint | undefined
    amount?: bigint | undefined
  }
  eventName: string
}

export const processData = async (ninunoBalanceClaimedEvent: BreedEvent) => {
  // TODO: start database transaction and revert upon fail
  try {
    const findClaimNinunoReward = await ClaimNinunoReward.findBy(
      'id',
      Number(ninunoBalanceClaimedEvent.args.withdrawalRequestId)
    )

    if (!findClaimNinunoReward) {
      throw new Error('withdrawalRequestId not found')
    }

    findClaimNinunoReward.processed = ClaimNinunoRewardProcessedStatus.PROCESSED
    await findClaimNinunoReward.save()

    const findUser = await User.findBy('blockchainAddress', ninunoBalanceClaimedEvent.address)

    if (findUser) {
      findUser.claimedBalance += BigInt(ninunoBalanceClaimedEvent.args.amount)
      await findUser.save()
    }

    return {
      isSuccess: true,
      amount: BigInt(ninunoBalanceClaimedEvent.args.amount),
    }
  } catch (error) {
    console.log('Error processing ninuno balance claimed event:', error)
    return {
      isSuccess: false,
    }
  }
}

export const processNinunoBalanceClaimedEvent = async (
  ninunoBalanceClaimedEvents: NinunoBalanceClaimedEventType[]
) => {
  for (const ninunoBalanceClaimedEvent of ninunoBalanceClaimedEvents) {
    await Bull.add(
      new ProcessNinunoBalanceClaimedEventJob().key,
      JSON.parse(
        JSON.stringify(ninunoBalanceClaimedEvent, (_, value) => {
          if (typeof value === 'bigint') {
            return value.toString()
          } else {
            return value
          }
        })
      ),
      {
        attempts: 1,
      }
    )
  }
}
