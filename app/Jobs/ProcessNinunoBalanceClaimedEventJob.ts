import { JobContract } from '@ioc:Rocketseat/Bull'
import Env from '@ioc:Adonis/Core/Env'
import BreedEvent, { BreedEventProcessedStatus } from 'App/Models/BreedEvent'
import { processData } from 'App/Services/ProcessNinunoBalanceClaimedEventService'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessNinunoBalanceClaimedEventJob implements JobContract {
  public key = `ProcessNinunoBalanceClaimedEventJob-${Env.get('NODE_ENV')}`

  public async handle(job) {
    const { data } = job
    const ninunoBalanceClaimedEvent = data

    try {
      const findEvent = await BreedEvent.query()
        .where('transactionHash', ninunoBalanceClaimedEvent.transactionHash)
        .where('logIndex', ninunoBalanceClaimedEvent.logIndex)
        .first()

      if (findEvent && findEvent.processed === BreedEventProcessedStatus.PROCESSED) {
        console.log('SKIP (Event already processed):', ninunoBalanceClaimedEvent.transactionHash)
        return {
          status: 0,
          message: 'Event already processed',
        }
      }

      if (findEvent && findEvent.processed === BreedEventProcessedStatus.PENDING) {
        console.log(
          'Reprocessing ninuno balance claimed event:',
          ninunoBalanceClaimedEvent.transactionHash
        )

        // process here
        const { isSuccess, amount } = await processData(findEvent)

        // after processing
        if (isSuccess) {
          findEvent.processed = BreedEventProcessedStatus.PROCESSED
          await findEvent.save()

          console.log('DONE:', findEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${findEvent.transactionHash}`,
            data: {
              amount: amount?.toString(),
            },
          }
        }
      } else {
        console.log(
          'Processing ninuno balance claimed event:',
          ninunoBalanceClaimedEvent.transactionHash
        )
        const createEvent = await BreedEvent.create({
          address: ninunoBalanceClaimedEvent.address,
          blockHash: ninunoBalanceClaimedEvent.blockHash,
          blockNumber: ninunoBalanceClaimedEvent.blockNumber,
          data: ninunoBalanceClaimedEvent.data,
          logIndex: ninunoBalanceClaimedEvent.logIndex,
          transactionHash: ninunoBalanceClaimedEvent.transactionHash,
          transactionIndex: ninunoBalanceClaimedEvent.transactionIndex,
          removed: ninunoBalanceClaimedEvent.removed,
          args: ninunoBalanceClaimedEvent.args,
          eventName: ninunoBalanceClaimedEvent.eventName,
          processed: BreedEventProcessedStatus.PENDING,
        })

        // process here
        const { isSuccess, amount } = await processData(createEvent)

        // after processing
        if (isSuccess) {
          createEvent.processed = BreedEventProcessedStatus.PROCESSED
          await createEvent.save()

          console.log('DONE:', createEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${createEvent.transactionHash}`,
            data: {
              amount: amount?.toString(),
            },
          }
        }
      }
    } catch (error) {
      console.log('Error processing ninuno balance claimed event:', error)
      return {
        status: 0,
        message: error.message,
      }
    }
  }
}
