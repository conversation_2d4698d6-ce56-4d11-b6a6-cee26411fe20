import { JobContract } from '@ioc:Rocketseat/Bull'
import Env from '@ioc:Adonis/Core/Env'
import { getLatestAssetPrices } from 'App/Helper/asset-prices'
import BreedingFee from 'App/Models/BreedingFee'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessCockConversionJob implements JobContract {
  public key = `ProcessCockConversionJob-${Env.get('NODE_ENV')}`

  public async handle(job) {
    const { data } = job
    // Do somethign with you job data

    try {
      const { cockPrice } = await getLatestAssetPrices()

      if (!cockPrice) {
        throw new Error('Failed to fetch $COCK price')
      }

      const getAllBreedingFee = await BreedingFee.all()

      for (const breedingFee of getAllBreedingFee) {
        breedingFee.cock = breedingFee.cockUsd / cockPrice
        await breedingFee.save()
      }

      return getAllBreedingFee
    } catch (error) {
      console.log('Error processing cock conversion:', error)
    }

    return data
  }
}
