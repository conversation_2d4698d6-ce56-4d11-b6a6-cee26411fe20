import { JobContract } from '@ioc:Rocketseat/Bull'
import Env from '@ioc:Adonis/Core/Env'
import BreedEvent, { BreedEventProcessedStatus } from 'App/Models/BreedEvent'
import { incrementParentBreedCount, processData } from 'App/Services/ProcessBreedEventService'

/*
|--------------------------------------------------------------------------
| Job setup
|--------------------------------------------------------------------------
|
| This is the basic setup for creating a job, but you can override
| some settings.
|
| You can get more details by looking at the bullmq documentation.
| https://docs.bullmq.io/
*/

export default class ProcessBreedEventJob implements JobContract {
  public key = `ProcessBreedEventJob-${Env.get('NODE_ENV')}`

  public async handle(job) {
    const { data } = job
    const breedEvent = data

    // Do somethign with you job data
    try {
      const findEvent = await BreedEvent.query()
        .where('transactionHash', breedEvent.transactionHash)
        .where('logIndex', breedEvent.logIndex)
        .first()

      if (findEvent && findEvent.processed === BreedEventProcessedStatus.PROCESSED) {
        console.log('SKIP (Event already processed):', breedEvent.transactionHash)
        return {
          status: 0,
          message: 'Event already processed',
        }
      }

      if (findEvent && findEvent.processed === BreedEventProcessedStatus.PENDING) {
        console.log('Reprocessing breed event:', breedEvent.transactionHash)

        // process here
        const { isSuccess, totalDistributed, amountToNinuno } = await processData(findEvent)

        // after processing
        if (isSuccess) {
          findEvent.processed = BreedEventProcessedStatus.PROCESSED
          await findEvent.save()

          await incrementParentBreedCount(
            Number(findEvent.args.chickenLeftTokenId),
            Number(findEvent.args.breedingCooldownTime)
          )
          await incrementParentBreedCount(
            Number(findEvent.args.chickenRightTokenId),
            Number(findEvent.args.breedingCooldownTime)
          )
          console.log('DONE:', findEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${findEvent.transactionHash}`,
            data: {
              totalDistributed: totalDistributed?.toString(),
              amountToNinuno: amountToNinuno?.toString(),
            },
          }
        }
      } else {
        console.log('Processing breed event:', breedEvent.transactionHash)
        const createEvent = await BreedEvent.create({
          address: breedEvent.address,
          blockHash: breedEvent.blockHash,
          blockNumber: breedEvent.blockNumber,
          data: breedEvent.data,
          logIndex: breedEvent.logIndex,
          transactionHash: breedEvent.transactionHash,
          transactionIndex: breedEvent.transactionIndex,
          removed: breedEvent.removed,
          args: breedEvent.args,
          eventName: breedEvent.eventName,
          processed: BreedEventProcessedStatus.PENDING,
        })

        // process here
        const { isSuccess, totalDistributed, amountToNinuno } = await processData(createEvent)

        // after processing
        if (isSuccess) {
          createEvent.processed = BreedEventProcessedStatus.PROCESSED
          await createEvent.save()

          await incrementParentBreedCount(
            Number(createEvent.args.chickenLeftTokenId),
            Number(createEvent.args.breedingCooldownTime)
          )
          await incrementParentBreedCount(
            Number(createEvent.args.chickenRightTokenId),
            Number(createEvent.args.breedingCooldownTime)
          )
          console.log('DONE:', createEvent.transactionHash)
          return {
            status: 1,
            message: `DONE: ${createEvent.transactionHash}`,
            data: {
              totalDistributed: totalDistributed?.toString(),
              amountToNinuno: amountToNinuno?.toString(),
            },
          }
        }
      }
    } catch (error) {
      console.log('Error processing breed event:', error)
      return {
        status: 0,
        message: error.message,
      }
    }
  }
}
