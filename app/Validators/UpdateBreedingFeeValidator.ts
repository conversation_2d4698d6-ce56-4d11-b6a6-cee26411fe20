import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ValidatorReporter } from './Reporters/ValidatorReporter'

export default class UpdateBreedingFeeValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    breedingFeeId: schema.number([rules.exists({ table: 'breeding_fees', column: 'id' })]),
    cockUsd: schema.number.optional(),
    feathers: schema.number.optional(),
  })

  public reporter = ValidatorReporter

  public messages: CustomMessages = {
    required: '{{ field }} is required',
    unique: '{{ field }} is already taken',
    exists: '{{ field }} doesnt exists',
    string: '{{ field }} is not a valid string',
    number: '{{ field }} is not a valid number',
    minLength: '{{ field }} should have one or more value',
    range: '{{ field }} must be between {{ options.start }} and {{ options.stop }}',
  }
}
