import { schema, CustomMessages } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ValidatorReporter } from './Reporters/ValidatorReporter'

export default class InitiateBulkBreedingValidator {
  constructor(protected ctx: HttpContextContract) {}
  public schema = schema.create({
    address: schema.string(),
    data: schema.array().members(
      schema.object().members({
        chickenLeftTokenId: schema.number(),
        chickenRightTokenId: schema.number(),
        resources: schema.array.optional().members(schema.array().members(schema.number())),
      })
    ),
  })

  public reporter = ValidatorReporter

  public messages: CustomMessages = {
    required: '{{ field }} is required',
    unique: '{{ field }} is already exists',
    exists: '{{ field }} doesnt exists',
    string: '{{ field }} is not a valid string',
    number: '{{ field }} is not a valid number',
    minLength: '{{ field }} should have one or more value',
    range: '{{ field }} must be between {{ options.start }} and {{ options.stop }}',
  }
}
