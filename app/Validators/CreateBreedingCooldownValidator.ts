import { schema, CustomMessages, rules } from '@ioc:Adonis/Core/Validator'
import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { ValidatorReporter } from './Reporters/ValidatorReporter'

export default class CreateBreedingCooldownValidator {
  constructor(protected ctx: HttpContextContract) {}

  public schema = schema.create({
    count: schema.number([rules.unique({ table: 'breeding_cooldowns', column: 'count' })]),
    cooldown: schema.number(),
  })

  public reporter = ValidatorReporter

  public messages: CustomMessages = {
    'required': '{{ field }} is required',
    'unique': '{{ field }} is already exists',
    'exists': '{{ field }} doesnt exists',
    'string': '{{ field }} is not a valid string',
    'number': '{{ field }} is not a valid number',
    'file.size': '{{ field }} file size must be under {{ options.size }}',
    'file.extname': '{{ field }} file must have one of {{ options.extnames }} extension names',
    'minLength': '{{ field }} should have one or more value',
    'range': '{{ field }} must be between {{ options.start }} and {{ options.stop }}',
    'email.unique': 'The email is already registered.',
    'password.minLength': 'The password must be at least 8 characters long.',
    'password.confirmed': 'The password confirmation does not match.',
  }
}
