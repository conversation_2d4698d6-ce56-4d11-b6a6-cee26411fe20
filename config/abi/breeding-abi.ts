export const breedingAbi = [
  {
    inputs: [],
    stateMutability: 'nonpayable',
    type: 'constructor',
  },
  {
    inputs: [],
    name: 'BreedTime',
    type: 'error',
  },
  {
    inputs: [],
    name: 'ERC1155InsufficientBalance',
    type: 'error',
  },
  {
    inputs: [],
    name: 'ERC20InsufficientBalance',
    type: 'error',
  },
  {
    inputs: [],
    name: 'ErrERC20TransferFailed',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InsufficientNinunoBalance',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidArrayLength',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidInitialization',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidParents',
    type: 'error',
  },
  {
    inputs: [],
    name: 'InvalidSignature',
    type: 'error',
  },
  {
    inputs: [],
    name: 'NonceAlreadyUsed',
    type: 'error',
  },
  {
    inputs: [],
    name: 'NotInitializing',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'owner',
        type: 'address',
      },
    ],
    name: 'OwnableInvalidOwner',
    type: 'error',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'account',
        type: 'address',
      },
    ],
    name: 'OwnableUnauthorizedAccount',
    type: 'error',
  },
  {
    inputs: [],
    name: 'ReentrancyGuardReentrantCall',
    type: 'error',
  },
  {
    inputs: [],
    name: 'UnauthorizedOwner',
    type: 'error',
  },
  {
    inputs: [],
    name: 'WithdrawalRequestIdAlreadyUsed',
    type: 'error',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256[]',
        name: 'chickenLeftTokenIds',
        type: 'uint256[]',
      },
      {
        indexed: true,
        internalType: 'uint256[]',
        name: 'chickenRightTokenIds',
        type: 'uint256[]',
      },
      {
        indexed: false,
        internalType: 'uint256[]',
        name: 'newTokenIds',
        type: 'uint256[]',
      },
      {
        indexed: false,
        internalType: 'uint256[][][]',
        name: 'feathersData',
        type: 'uint256[][][]',
      },
      {
        indexed: false,
        internalType: 'uint256[][][]',
        name: 'resourcesData',
        type: 'uint256[][][]',
      },
    ],
    name: 'BatchBreed',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'chickenLeftTokenId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'uint256',
        name: 'chickenRightTokenId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'uint256',
        name: 'newTokenId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amountToNinuno',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256[][]',
        name: 'feathersData',
        type: 'uint256[][]',
      },
      {
        indexed: false,
        internalType: 'uint256[][]',
        name: 'resourcesData',
        type: 'uint256[][]',
      },
    ],
    name: 'Breed',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'uint256',
        name: 'chickenLeftTokenId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'uint256',
        name: 'chickenRightTokenId',
        type: 'uint256',
      },
      {
        indexed: true,
        internalType: 'uint256',
        name: 'newTokenId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amountToNinuno',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256[][]',
        name: 'feathersData',
        type: 'uint256[][]',
      },
      {
        indexed: false,
        internalType: 'uint256[][]',
        name: 'resourcesData',
        type: 'uint256[][]',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'breedingCooldownTime',
        type: 'uint256',
      },
    ],
    name: 'BreedV2',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: false,
        internalType: 'uint64',
        name: 'version',
        type: 'uint64',
      },
    ],
    name: 'Initialized',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'user',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'uint256',
        name: 'withdrawalRequestId',
        type: 'uint256',
      },
      {
        indexed: false,
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
    ],
    name: 'NinunoBalanceClaimed',
    type: 'event',
  },
  {
    anonymous: false,
    inputs: [
      {
        indexed: true,
        internalType: 'address',
        name: 'previousOwner',
        type: 'address',
      },
      {
        indexed: true,
        internalType: 'address',
        name: 'newOwner',
        type: 'address',
      },
    ],
    name: 'OwnershipTransferred',
    type: 'event',
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: 'uint256',
            name: 'chickenLeftTokenId',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'chickenRightTokenId',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'totalAmount',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'amountToVault',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'amountToNinuno',
            type: 'uint256',
          },
          {
            internalType: 'uint256',
            name: 'breedingCooldownTime',
            type: 'uint256',
          },
          {
            internalType: 'uint256[][]',
            name: 'feathersData',
            type: 'uint256[][]',
          },
          {
            internalType: 'uint256[][]',
            name: 'resourcesData',
            type: 'uint256[][]',
          },
        ],
        internalType: 'struct SabongSagaBreedingUpgradeable.BreedingParams',
        name: 'params',
        type: 'tuple',
      },
      {
        internalType: 'bytes',
        name: '_sig',
        type: 'bytes',
      },
      {
        internalType: 'string',
        name: 'referralCode',
        type: 'string',
      },
    ],
    name: 'breed',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        components: [
          {
            internalType: 'uint256[]',
            name: 'chickenLeftTokenIds',
            type: 'uint256[]',
          },
          {
            internalType: 'uint256[]',
            name: 'chickenRightTokenIds',
            type: 'uint256[]',
          },
          {
            internalType: 'uint256[]',
            name: 'totalAmounts',
            type: 'uint256[]',
          },
          {
            internalType: 'uint256[]',
            name: 'amountsToVault',
            type: 'uint256[]',
          },
          {
            internalType: 'uint256[]',
            name: 'amountsToNinuno',
            type: 'uint256[]',
          },
          {
            internalType: 'uint256[]',
            name: 'breedingCooldownTimes',
            type: 'uint256[]',
          },
          {
            internalType: 'uint256[][][]',
            name: 'feathersData',
            type: 'uint256[][][]',
          },
          {
            internalType: 'uint256[][][]',
            name: 'resourcesData',
            type: 'uint256[][][]',
          },
          {
            internalType: 'bytes[]',
            name: 'signatures',
            type: 'bytes[]',
          },
        ],
        internalType: 'struct SabongSagaBreedingUpgradeable.BatchBreedingParams',
        name: 'params',
        type: 'tuple',
      },
      {
        internalType: 'string',
        name: 'referralCode',
        type: 'string',
      },
    ],
    name: 'breedBatch',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    name: 'chickenBreedCount',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    name: 'chickenBreedTime',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: 'withdrawalRequestId',
        type: 'uint256',
      },
      {
        internalType: 'uint256',
        name: 'amount',
        type: 'uint256',
      },
      {
        internalType: 'bytes',
        name: '_sig',
        type: 'bytes',
      },
    ],
    name: 'claimNinunoBalance',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'cock',
    outputs: [
      {
        internalType: 'contract ERC20Common',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'feathers',
    outputs: [
      {
        internalType: 'contract ERC1155Common',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'genesis',
    outputs: [
      {
        internalType: 'contract ERC721Common',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256[]',
        name: 'tokenIds',
        type: 'uint256[]',
      },
    ],
    name: 'getChickenBreedCountBatch',
    outputs: [
      {
        internalType: 'uint256[]',
        name: '',
        type: 'uint256[]',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256[]',
        name: 'tokenIds',
        type: 'uint256[]',
      },
    ],
    name: 'getChickenBreedTimeBatch',
    outputs: [
      {
        internalType: 'uint256[]',
        name: '',
        type: 'uint256[]',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: '_cockAddress',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '_genesisAddress',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '_legacyAddress',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '_feathersAddress',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '_resourcesAddress',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '_treasury',
        type: 'address',
      },
      {
        internalType: 'address',
        name: '_signer',
        type: 'address',
      },
    ],
    name: 'initialize',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256[]',
        name: 'tokenIds',
        type: 'uint256[]',
      },
    ],
    name: 'isGenesisBatch',
    outputs: [
      {
        internalType: 'bool[]',
        name: '',
        type: 'bool[]',
      },
    ],
    stateMutability: 'pure',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256[]',
        name: 'tokenIds',
        type: 'uint256[]',
      },
    ],
    name: 'isNinunoEligibleBatch',
    outputs: [
      {
        internalType: 'bool[]',
        name: '',
        type: 'bool[]',
      },
    ],
    stateMutability: 'pure',
    type: 'function',
  },
  {
    inputs: [],
    name: 'legacy',
    outputs: [
      {
        internalType: 'contract SabongSagaChickens',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'nonceTracker',
    outputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'owner',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'referral',
    outputs: [
      {
        internalType: 'contract IReferral',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'renounceOwnership',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'resources',
    outputs: [
      {
        internalType: 'contract ERC1155Common',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [],
    name: 'signer',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: 'newOwner',
        type: 'address',
      },
    ],
    name: 'transferOwnership',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [],
    name: 'treasury',
    outputs: [
      {
        internalType: 'address',
        name: '',
        type: 'address',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: '_referral',
        type: 'address',
      },
    ],
    name: 'updateReferral',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'address',
        name: '_signer',
        type: 'address',
      },
    ],
    name: 'updateSigner',
    outputs: [],
    stateMutability: 'nonpayable',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    name: 'usedNonces',
    outputs: [
      {
        internalType: 'bool',
        name: '',
        type: 'bool',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
  {
    inputs: [
      {
        internalType: 'uint256',
        name: '',
        type: 'uint256',
      },
    ],
    name: 'usedWithdrawalRequestIds',
    outputs: [
      {
        internalType: 'bool',
        name: '',
        type: 'bool',
      },
    ],
    stateMutability: 'view',
    type: 'function',
  },
] as const
