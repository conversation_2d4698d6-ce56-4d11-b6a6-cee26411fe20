# Deployment Documentation

This document outlines the deployment process for the Breeding API application.

## Prerequisites

Ensure the following tools are installed on your deployment server:

- Node.js (Latest LTS version recommended)
- PM2: `npm install -g pm2`
- Yarn: `npm install -g yarn`
- unzip: `sudo apt-get install unzip`
- tar: `sudo apt-get install tar`

## Environment Setup

1. Generate AdonisJS APP_KEY:

   ```bash
   node ace generate:key
   ```

This will generate a random 32-character key that looks like: `xxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx`

2. Create `.env` file in the root directory with required environment variables:

   ```bash
   PORT=3333
   HOST=0.0.0.0
   NODE_ENV=development
   APP_KEY=eys7ivTz4FN3HmmlmcKwu5Z6-tRbEEZx
   DRIVE_DISK=local
   DB_CONNECTION=mysql
   MYSQL_HOST=localhost
   MYSQL_PORT=3306
   MYSQL_USER=lucid
   MYSQL_PASSWORD=
   MYSQL_DB_NAME=lucid
   REDIS_CONNECTION=local
   REDIS_HOST=127.0.0.1
   REDIS_PORT=6379
   REDIS_PASSWORD=
   SIGNER_KEY=privateKey
   RONIN_RPC=
   CHICKEN_GENESIS_ADDRESS=
   CHICKEN_LEGACY_ADDRESS=
   COCK_ADDRESS=
   ITEMS_ADDRESS=
   RESOURCES_ADDRESS=
   REFERRAL_ADDRESS=
   BREEDING_ADDRESS=
   BREEDING_COOLDOWN_MAX_COUNT_DURATION=7
   BREEDING_COOLDOWN_MAX_COUNT_FEE=7
   BREEDING_NINUNO_PERCENTAGE_PER_PARENT=2.5
   BREEDING_HATCHING_DAYS=604800 # in seconds
   BREEDING_CREATION_BLOCKNUMBER=********
   DUMMY_ACCOUNT_KEY=privateKey # optional, for testing purposes
   ADMIN_ADDRESSES=address1,address2
   API_URL=http://localhost:3333
   BULL_REDIS_HOST=127.0.0.1
   BULL_REDIS_PORT=6379
   BULL_REDIS_PASSWORD=
   BULL_DASHBOARD_PORT=9999
   CHICKEN_IVORY_API_URL=
   CHICKEN_IVORY_API_KEY=
   JWT_SECRET=
   ```

## Directory Structure

The deployment script expects the following structure:

```
.
├── feature/
│   └── hashlips/
│       └── layers/          # Chicken NFT layers
├── metadata.zip            # Required metadata files
├── deploy.sh              # Deployment script
└── .env                   # Environment configuration
```

## Deployment Process

### 1. Manual Deployment

```bash
# Using yarn
yarn deploy:prepare

# Or using npm
npm run deploy:prepare
```

The deployment script performs the following steps:

1. Checks for required commands and files
2. Creates backup of existing tmp folder
3. Installs dependencies
4. Builds the application
5. Copies files to deployment directory
6. Sets up PM2 configuration
7. Extracts metadata
8. Copies layers directory

### 2. Directory Structure After Deployment

```
deployment/
├── feature/
│   └── hashlips/
│       └── layers/          # Chicken NFT layers
├── tmp/
│   └── uploads/            # Upload directory
├── server.js              # Main application file
├── .env                   # Environment configuration
└── ecosystem.config.js    # PM2 configuration
```

### 3. Database Seeder (First Time Only)

For first-time deployment, you need to seed initial data:

1. Seed the database with initial data:
   ```bash
   node ace db:seed
   ```
   This will run all seeders, including:
   - Initial NFT data
   - User data
   - Other required base data

⚠️ IMPORTANT: These steps should only be executed once during the first deployment. Running them again may cause duplicate data or other issues.

### 4. Start PM2 for Deployment

```bash
# Using yarn
yarn deploy:start

# Or using npm
npm run deploy:start
```

### 4. Backup System

The deployment script automatically backs up only the generated files in the tmp folder before each deployment:

- Location: `./backups/`
- Format: `tmp_backup_YYYYMMDD_HHMMSS.tar.gz`
- Contents: Only files from `deployment/tmp/` directory, which includes:
  - Uploaded files
  - Temporary generated assets
  - User uploads

This ensures that user-generated content and temporary files are preserved between deployments.

## PM2 Process Management

### View Application Status

```bash
pm2 status
```

### View Logs

```bash
pm2 log breeding-api
```

### Restart Application

```bash
pm2 restart breeding-api
```

### Stop Application

```bash
pm2 stop breeding-api
```

## Troubleshooting

### Common Issues

1. **Permission Denied**

   ```bash
   chmod +x ./deploy.sh
   ```

2. **PM2 Process Not Found**

   ```bash
   pm2 delete breeding-api
   yarn deploy
   ```

3. **Missing Dependencies**
   ```bash
   cd deployment
   yarn install
   ```

### Verification Steps

1. Check if the application is running:

   ```bash
   pm2 status
   ```

2. Check logs for errors:
   ```bash
   pm2 log breeding-api
   ```
