import Env from '@ioc:Adonis/Core/Env'
import { test } from '@japa/runner'
import SABONG_CONFIG from 'Config/sabong'
import { createWalletClient, http, recoverMessageAddress } from 'viem'
import { privateKeyToAccount } from 'viem/accounts'
import { ronin, saigon } from 'viem/chains'

const walletClient = createWalletClient({
  chain: SABONG_CONFIG.ENV === 'production' ? ronin : saigon,
  transport: http(SABONG_CONFIG.RONIN_RPC),
})

test.group('Test login', () => {
  // Write your test here
  test('Test Request/Verify', async ({ assert }) => {
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    const signature = await walletClient.signMessage({
      account,
      message: 'hello world',
    })

    const address = await recoverMessageAddress({
      message: 'hello world',
      signature,
    })

    console.log('address: ', address.toLowerCase())
    console.log('account.address: ', account.address.toLowerCase())

    assert.equal(address.toLowerCase(), account.address.toLowerCase())
  })

  test('Test Login', async ({ client }) => {
    const ownerWalletAddress = '******************************************'

    // REQUEST
    const requestResponse = await client.post('/auth/request').json({
      address: ownerWalletAddress,
    })

    console.log(requestResponse.body())

    requestResponse.assertStatus(200)

    // VERIFY
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    console.log(requestResponse.body().data.message)
    const signature = await walletClient.signMessage({
      account,
      message: requestResponse.body().data,
    })

    const verifyResponse = await client.post('/auth/verify').json({
      address: ownerWalletAddress,
      signature,
    })

    console.log(verifyResponse.body())

    verifyResponse.assertStatus(200)
  })
})
