import Env from '@ioc:Adonis/Core/Env'
import { test } from '@japa/runner'
import { initializeContracts } from 'App/Helper/contracts'
import SABONG_CONFIG from 'Config/sabong'
import { createWalletClient, http } from 'viem'
import { privateKeyToAccount } from 'viem/accounts'
import { ronin, saigon } from 'viem/chains'

const walletClient = createWalletClient({
  chain: SABONG_CONFIG.ENV === 'production' ? ronin : saigon,
  transport: http(SABONG_CONFIG.RONIN_RPC),
})

test.group('Test initiate breeding', () => {
  // Write your test here
  test('Test initiate breeding', async ({ client }) => {
    const ownerWalletAddress = '******************************************'

    // REQUEST
    const requestResponse = await client.post('/auth/request').json({
      address: ownerWalletAddress,
    })

    console.log(requestResponse.body())

    requestResponse.assertStatus(200)

    // VERIFY
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    console.log(requestResponse.body().data)
    const signature = await walletClient.signMessage({
      account,
      message: requestResponse.body().data,
    })

    const verifyResponse = await client.post('/auth/verify').json({
      address: ownerWalletAddress,
      signature,
    })

    console.log(verifyResponse.body())

    verifyResponse.assertStatus(200)

    const response = await client
      .post('/breedings/initiate-breeding')
      .json({
        address: '******************************************',
        chickenLeftTokenId: 2231,
        chickenRightTokenId: 6,
      })
      .headers({
        Authorization: 'Bearer ' + verifyResponse.body().token,
      })

    console.log(response.body())

    response.assertStatus(200)
  })

  test('Test complete breeding', async ({ client }) => {
    const ownerWalletAddress = '******************************************'

    // REQUEST
    const requestResponse = await client.post('/auth/request').json({
      address: ownerWalletAddress,
    })

    console.log(requestResponse.body())

    requestResponse.assertStatus(200)

    // VERIFY
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    console.log(requestResponse.body().data)
    const loginSignature = await walletClient.signMessage({
      account,
      message: requestResponse.body().data,
    })

    const verifyResponse = await client.post('/auth/verify').json({
      address: ownerWalletAddress,
      signature: loginSignature,
    })

    console.log(verifyResponse.body())

    verifyResponse.assertStatus(200)

    const response = await client
      .post('/breedings/initiate-breeding')
      .json({
        address: '******************************************',
        chickenLeftTokenId: 20,
        chickenRightTokenId: 2240,
      })
      .headers({
        Authorization: 'Bearer ' + verifyResponse.body().token,
      })

    console.log(response.body())

    response.assertStatus(200)

    // CONTRACT INTERACTION (FRONTEND)
    const {
      chickenLeftTokenId,
      chickenRightTokenId,
      totalAmount,
      amountToVault,
      amountToNinuno,
      feathersData,
      resourcesData,
      breedingCooldownTime,
      signature,
    } = response.body().data
    const { itemContract, cockContract, breedingContract, resourcesContract } =
      initializeContracts()

    console.log(response.body().data)

    // APPROVED COCK
    const approveCock = await cockContract.write.approve(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, totalAmount],
      {
        account,
      }
    )

    console.log('approveCock: ', approveCock)

    // APPROVED FEATHERS
    const approveFeather = await itemContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveFeather: ', approveFeather)

    // APPROVED RESOURCES
    const approveResources = await resourcesContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveResources: ', approveResources)

    const breed = await breedingContract.write.breed(
      [
        {
          chickenLeftTokenId: BigInt(chickenLeftTokenId),
          chickenRightTokenId: BigInt(chickenRightTokenId),
          totalAmount: BigInt(totalAmount),
          amountToVault: BigInt(amountToVault),
          amountToNinuno: BigInt(amountToNinuno),
          breedingCooldownTime: BigInt(breedingCooldownTime),
          feathersData: feathersData.map((arr) => arr.map((n) => BigInt(n))),
          resourcesData: resourcesData.map((arr) => arr.map((n) => BigInt(n))),
        },
        signature,
        '', // referral code
      ],
      { account }
    )

    console.log(breed)
  })

  test('Test initiate bulk breeding', async ({ client }) => {
    const ownerWalletAddress = '******************************************'

    // REQUEST
    const requestResponse = await client.post('/auth/request').json({
      address: ownerWalletAddress,
    })

    console.log(requestResponse.body())

    requestResponse.assertStatus(200)

    // VERIFY
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    console.log(requestResponse.body().data)
    const signature = await walletClient.signMessage({
      account,
      message: requestResponse.body().data,
    })

    const verifyResponse = await client.post('/auth/verify').json({
      address: ownerWalletAddress,
      signature,
    })

    console.log(verifyResponse.body())

    verifyResponse.assertStatus(200)

    const response = await client
      .post('/breedings/initiate-bulk-breeding')
      .json({
        address: '******************************************',
        data: [
          {
            chickenLeftTokenId: 21,
            chickenRightTokenId: 2241,
          },
          {
            chickenLeftTokenId: 2231,
            chickenRightTokenId: 6,
          },
        ],
      })
      .headers({
        Authorization: 'Bearer ' + verifyResponse.body().token,
      })

    console.log(response.body())

    response.assertStatus(200)
  })

  test('Test complete bulk breeding', async ({ client }) => {
    const ownerWalletAddress = '******************************************'

    // REQUEST
    const requestResponse = await client.post('/auth/request').json({
      address: ownerWalletAddress,
    })

    console.log(requestResponse.body())

    requestResponse.assertStatus(200)

    // VERIFY
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    console.log(requestResponse.body().data)
    const loginSignature = await walletClient.signMessage({
      account,
      message: requestResponse.body().data,
    })

    const verifyResponse = await client.post('/auth/verify').json({
      address: ownerWalletAddress,
      signature: loginSignature,
    })

    console.log(verifyResponse.body())

    verifyResponse.assertStatus(200)

    const response = await client
      .post('/breedings/initiate-bulk-breeding')
      .json({
        address: '******************************************',
        data: [
          {
            chickenLeftTokenId: 21,
            chickenRightTokenId: 2241,
          },
          {
            chickenLeftTokenId: 2231,
            chickenRightTokenId: 6,
          },
        ],
      })
      .headers({
        Authorization: 'Bearer ' + verifyResponse.body().token,
      })

    console.log(response.body())

    response.assertStatus(200)

    // CONTRACT INTERACTION (FRONTEND)
    const {
      address,
      chickenLeftTokenIds,
      chickenRightTokenIds,
      totalAmounts,
      amountsToVault,
      amountsToNinuno,
      breedingCooldownTimes,
      feathersData,
      resourcesData,
      signatures,
    }: {
      address: string
      chickenLeftTokenIds: string[]
      chickenRightTokenIds: string[]
      totalAmounts: string[]
      amountsToVault: string[]
      amountsToNinuno: string[]
      breedingCooldownTimes: string[]
      feathersData: string[][][]
      resourcesData: string[][][]
      signatures: `0x${string}`[]
    } = response.body().data
    const { itemContract, cockContract, breedingContract, resourcesContract } =
      initializeContracts()

    console.log(response.body().data)
    console.log(address)
    console.log(
      'approveCock: ',
      totalAmounts.reduce((a, b) => a + BigInt(b), 0n)
    )
    // APPROVED COCK
    const approveCock = await cockContract.write.approve(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, totalAmounts.reduce((a, b) => a + BigInt(b), 0n)],
      {
        account,
      }
    )

    console.log('approveCock: ', approveCock)

    // APPROVED FEATHERS
    const approveFeather = await itemContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveFeather: ', approveFeather)

    // APPROVED RESOURCES
    const approveResources = await resourcesContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveResources: ', approveResources)

    const breedBatch = await breedingContract.write.breedBatch(
      [
        {
          chickenLeftTokenIds: chickenLeftTokenIds.map((item) => BigInt(item)),
          chickenRightTokenIds: chickenRightTokenIds.map((item) => BigInt(item)),
          totalAmounts: totalAmounts.map((item) => BigInt(`${item}`)),
          amountsToVault: amountsToVault.map((item) => BigInt(`${item}`)),
          amountsToNinuno: amountsToNinuno.map((item) => BigInt(`${item}`)),
          breedingCooldownTimes: breedingCooldownTimes.map((item) => BigInt(item)),
          feathersData: feathersData.map((arr) => arr.map((n) => n.map((o) => BigInt(o)))),
          resourcesData: resourcesData.map((arr) => arr.map((n) => n.map((o) => BigInt(o)))),
          signatures,
        },
        '', // referral code
      ],
      { account }
    )

    console.log(breedBatch)
  })

  test('Test complete 10 bulk breeding', async ({ client }) => {
    const ownerWalletAddress = '******************************************'

    // REQUEST
    const requestResponse = await client.post('/auth/request').json({
      address: ownerWalletAddress,
    })

    console.log(requestResponse.body())

    requestResponse.assertStatus(200)

    // VERIFY
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    console.log(requestResponse.body().data)
    const loginSignature = await walletClient.signMessage({
      account,
      message: requestResponse.body().data,
    })

    const verifyResponse = await client.post('/auth/verify').json({
      address: ownerWalletAddress,
      signature: loginSignature,
    })

    console.log(verifyResponse.body())

    verifyResponse.assertStatus(200)

    const response = await client
      .post('/breedings/initiate-bulk-breeding')
      .json({
        address: '******************************************',
        data: [
          {
            chickenLeftTokenId: 22,
            chickenRightTokenId: 2242,
          },
          {
            chickenLeftTokenId: 23,
            chickenRightTokenId: 2243,
          },
          {
            chickenLeftTokenId: 24,
            chickenRightTokenId: 2244,
          },
          {
            chickenLeftTokenId: 25,
            chickenRightTokenId: 2245,
          },
          {
            chickenLeftTokenId: 26,
            chickenRightTokenId: 2246,
          },
          {
            chickenLeftTokenId: 27,
            chickenRightTokenId: 2247,
          },
          {
            chickenLeftTokenId: 28,
            chickenRightTokenId: 2248,
          },
          {
            chickenLeftTokenId: 29,
            chickenRightTokenId: 2249,
          },
          {
            chickenLeftTokenId: 30,
            chickenRightTokenId: 2250,
          },
          {
            chickenLeftTokenId: 31,
            chickenRightTokenId: 2251,
          },
        ],
      })
      .headers({
        Authorization: 'Bearer ' + verifyResponse.body().token,
      })

    console.log(response.body())

    response.assertStatus(200)

    // CONTRACT INTERACTION (FRONTEND)
    const {
      address,
      chickenLeftTokenIds,
      chickenRightTokenIds,
      totalAmounts,
      amountsToVault,
      amountsToNinuno,
      breedingCooldownTimes,
      feathersData,
      resourcesData,
      signatures,
    }: {
      address: string
      chickenLeftTokenIds: string[]
      chickenRightTokenIds: string[]
      totalAmounts: string[]
      amountsToVault: string[]
      amountsToNinuno: string[]
      breedingCooldownTimes: string[]
      feathersData: string[][][]
      resourcesData: string[][][]
      signatures: `0x${string}`[]
    } = response.body().data
    const { itemContract, cockContract, breedingContract, resourcesContract } =
      initializeContracts()

    console.log(response.body().data)
    console.log(address)
    console.log(
      'approveCock: ',
      totalAmounts.reduce((a, b) => a + BigInt(b), 0n)
    )
    // APPROVED COCK
    const approveCock = await cockContract.write.approve(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, totalAmounts.reduce((a, b) => a + BigInt(b), 0n)],
      {
        account,
      }
    )

    console.log('approveCock: ', approveCock)

    // APPROVED FEATHERS
    const approveFeather = await itemContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveFeather: ', approveFeather)

    // APPROVED RESOURCES
    const approveResources = await resourcesContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveResources: ', approveResources)

    const breedBatch = await breedingContract.write.breedBatch(
      [
        {
          chickenLeftTokenIds: chickenLeftTokenIds.map((item) => BigInt(item)),
          chickenRightTokenIds: chickenRightTokenIds.map((item) => BigInt(item)),
          totalAmounts: totalAmounts.map((item) => BigInt(`${item}`)),
          amountsToVault: amountsToVault.map((item) => BigInt(`${item}`)),
          amountsToNinuno: amountsToNinuno.map((item) => BigInt(`${item}`)),
          breedingCooldownTimes: breedingCooldownTimes.map((item) => BigInt(item)),
          feathersData: feathersData.map((arr) => arr.map((n) => n.map((o) => BigInt(o)))),
          resourcesData: resourcesData.map((arr) => arr.map((n) => n.map((o) => BigInt(o)))),
          signatures,
        },
        '', // referral code
      ],
      { account }
    )

    console.log(breedBatch)
  })
})
