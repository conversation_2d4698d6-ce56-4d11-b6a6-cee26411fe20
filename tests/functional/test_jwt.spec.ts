import { test } from '@japa/runner'

test.group('Test jwt', () => {
  // Write your test here
  test('Test jwt', async ({ client }) => {
    const token =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZGRyZXNzIjoiMHhlZDNmMDQ5YjFhMzk2NDk1ZDNiMGJlYTlhMTM2MjJjZTZlOGMyZmQ3IiwiZXhwIjoxNzQzMTIzNDMwLCJ0b2tlblR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NDMwMzcwMzB9.OxM7OsP0femAgjiUVxH33dPnYcG6BUsWI1sV-O80y3w'

    const response = await client.get('/me').headers({
      Authorization: 'Bearer ' + token,
    })

    console.log(response.body())

    response.assertStatus(200)
  })

  test('Test jwt with admin middleware', async ({ client }) => {
    const token =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJhZGRyZXNzIjoiMHhlZDNmMDQ5YjFhMzk2NDk1ZDNiMGJlYTlhMTM2MjJjZTZlOGMyZmQ3IiwiZXhwIjoxNzQzMTIzNDMwLCJ0b2tlblR5cGUiOiJhY2Nlc3MiLCJpYXQiOjE3NDMwMzcwMzB9.OxM7OsP0femAgjiUVxH33dPnYcG6BUsWI1sV-O80y3w'

    const response = await client.get('/admin/breeding-fees').headers({
      Authorization: 'Bearer ' + token,
    })

    console.log(response.body())

    response.assertStatus(200)
  })
})
