import { test } from '@japa/runner'
import SABONG_CONFIG from 'Config/sabong'
import { DateTime } from 'luxon'
import { createPublicClient, formatUnits, getContract, http, parseEther } from 'viem'
import { ronin, saigon } from 'viem/chains'
import { encodePacked, keccak256, encodeAbiParameters } from 'viem'
import { privateKeyToAccount, signMessage } from 'viem/accounts'
import Env from '@ioc:Adonis/Core/Env'

const BREEDING_COOLDOWN = [
  {
    count: 0, //breeding count
    cooldown: 1, //cooldown duration in days
  },
  {
    count: 1,
    cooldown: 3,
  },
  {
    count: 2,
    cooldown: 7,
  },
  {
    count: 3,
    cooldown: 15,
  },
  {
    count: 4,
    cooldown: 40,
  },
  {
    count: 5,
    cooldown: 90,
  },
  {
    count: 6,
    cooldown: 180,
  },
  {
    count: 7,
    cooldown: 365,
  },
]

const BREEDING_FEES = [
  {
    count: 0, //total breeding count
    cock: 10, //cooldown duration in days
    feathers: 10, //cooldown duration in days
  },
  {
    count: 1,
    cock: 150,
    feathers: 120,
  },
  {
    count: 2,
    cock: 300,
    feathers: 240,
  },
  {
    count: 3,
    cock: 600,
    feathers: 480,
  },
  {
    count: 4,
    cock: 1200,
    feathers: 960,
  },
  {
    count: 5,
    cock: 2400,
    feathers: 1800,
  },
  {
    count: 6,
    cock: 4800,
    feathers: 3000,
  },
  {
    count: 7,
    cock: 9600,
    feathers: 5000,
  },
]

test.group('Test Group', () => {
  test('Test breed', async () => {
    const client = createPublicClient({
      chain: SABONG_CONFIG.ENV === 'production' ? ronin : saigon,
      transport: http(SABONG_CONFIG.RONIN_RPC),
    })

    const chickenLegacyContract = getContract({
      address: SABONG_CONFIG.CONTRACTS.CHICKEN_LEGACY_ADDRESS,
      abi: SABONG_CONFIG.ABIS.CHICKEN_LEGACY_ABI,
      client: client,
    })

    const chickenGenesisContract = getContract({
      address: SABONG_CONFIG.CONTRACTS.CHICKEN_GENESIS_ADDRESS,
      abi: SABONG_CONFIG.ABIS.CHICKEN_GENESIS_ABI,
      client: client,
    })

    const itemContract = getContract({
      address: SABONG_CONFIG.CONTRACTS.ITEMS_ADDRESS,
      abi: SABONG_CONFIG.ABIS.ITEMS_ABI,
      client: client,
    })

    const cockContract = getContract({
      address: SABONG_CONFIG.CONTRACTS.COCK_ADDRESS,
      abi: SABONG_CONFIG.ABIS.COCK_ABI,
      client: client,
    })

    const breedingContract = getContract({
      address: SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS,
      abi: SABONG_CONFIG.ABIS.BREEDING_ABI,
      client: client,
    })

    const resourcesContract = getContract({
      address: SABONG_CONFIG.CONTRACTS.RESOURCES_ADDRESS,
      abi: SABONG_CONFIG.ABIS.RESOURCES_ABI,
      client: client,
    })

    // Sample Data
    const ownerWalletAddress = '******************************************'
    const parent1 = 2231
    const parent2 = 6

    // ------

    // Check parent ownership
    const parent1Owner =
      parent1 > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
        ? await chickenLegacyContract.read.ownerOf([BigInt(parent1)])
        : await chickenGenesisContract.read.ownerOf([BigInt(parent1)])

    console.log(parent1Owner)

    if (ownerWalletAddress.toLowerCase() !== parent1Owner.toLowerCase()) {
      throw new Error('Parent 1 is not owned by the owner wallet address')
    }

    const parent2Owner =
      parent2 > SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD
        ? await chickenLegacyContract.read.ownerOf([BigInt(parent2)])
        : await chickenGenesisContract.read.ownerOf([BigInt(parent2)])

    console.log(parent2Owner)

    if (ownerWalletAddress.toLowerCase() !== parent2Owner.toLowerCase()) {
      throw new Error('Parent 2 is not owned by the owner wallet address')
    }

    // Check breeding cooldown
    const parentBreedingCooldown = (
      await breedingContract.read.getChickenBreedTimeBatch([[BigInt(parent1), BigInt(parent2)]])
    ).map((item) => Number(item))

    console.log('parentBreedingCooldown: ', parentBreedingCooldown)

    const isParent1DoneCooldown = parentBreedingCooldown[0] * 1000 <= DateTime.now().toMillis()

    console.log('isParent1DoneCooldown: ', isParent1DoneCooldown)

    if (!isParent1DoneCooldown) {
      throw new Error('Parent 1 is not done with its breeding cooldown')
    }

    const isParent2DoneCooldown = parentBreedingCooldown[1] * 1000 <= DateTime.now().toMillis()

    console.log('isParent2DoneCooldown: ', isParent2DoneCooldown)

    if (!isParent2DoneCooldown) {
      throw new Error('Parent 2 is not done with its breeding cooldown')
    }

    const parentBreedingCount = (
      await breedingContract.read.getChickenBreedCountBatch([[BigInt(parent1), BigInt(parent2)]])
    ).map((item) => Number(item))

    const getParent1CooldownDuration = BREEDING_COOLDOWN.find((cooldown) => {
      if (parentBreedingCount[0] >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION) return true
      return cooldown.count === parentBreedingCount[0]
    })

    console.log('getParent1CooldownDuration: ', getParent1CooldownDuration)

    if (!getParent1CooldownDuration) {
      throw new Error('Parent 1 does not have a valid breeding cooldown duration')
    }

    const getParent2CooldownDuration = BREEDING_COOLDOWN.find((cooldown) => {
      if (parentBreedingCount[1] >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_DURATION) return true
      return cooldown.count === parentBreedingCount[1]
    })

    console.log('getParent2CooldownDuration: ', getParent2CooldownDuration)

    if (!getParent2CooldownDuration) {
      throw new Error('Parent 2 does not have a valid breeding cooldown duration')
    }

    const totalBreedingCooldown =
      getParent1CooldownDuration.cooldown + getParent2CooldownDuration.cooldown

    console.log('totalBreedingCooldown: ', totalBreedingCooldown)

    // check $COCK & Feather cost base on totalBreedingCount
    // const totalBreedingCount = parentBreedingCount[0] + parentBreedingCount[1]

    const getParent1BreedingCost = BREEDING_FEES.find((cost) => {
      if (parentBreedingCount[0] >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE) return true
      return cost.count === parentBreedingCount[0]
    })

    console.log('getParent1BreedingCost: ', getParent1BreedingCost)

    if (!getParent1BreedingCost) {
      throw new Error('Cannot estimate breeding cost!')
    }

    const getParent2BreedingCost = BREEDING_FEES.find((cost) => {
      if (parentBreedingCount[1] >= SABONG_CONFIG.BREEDING_COOLDOWN_MAX_COUNT_FEE) return true
      return cost.count === parentBreedingCount[1]
    })

    console.log('getParent2BreedingCost: ', getParent2BreedingCost)

    if (!getParent2BreedingCost) {
      throw new Error('Cannot estimate breeding cost!')
    }

    const checkCockBalanceRaw = await cockContract.read.balanceOf([ownerWalletAddress])
    const checkCockDecimals = await cockContract.read.decimals()
    const checkCockBalance = Number(formatUnits(checkCockBalanceRaw, checkCockDecimals))

    console.log('checkCockBalance: ', checkCockBalance)

    if (getParent1BreedingCost.cock + getParent2BreedingCost.cock > checkCockBalance) {
      throw new Error('Does not have enough $COCK')
    }

    const checkFeatherBalance = Number(
      await itemContract.read.balanceOf([ownerWalletAddress, BigInt(1)])
    )

    console.log('checkFeatherBalance: ', checkFeatherBalance)

    if (getParent1BreedingCost.feathers + getParent2BreedingCost.feathers > checkFeatherBalance) {
      throw new Error('Does not have enough FEATHER')
    }

    const breedingCockFee = getParent1BreedingCost.cock + getParent2BreedingCost.cock
    const totalAmount = breedingCockFee
    const amountToVault =
      ((100 - SABONG_CONFIG.BREEDING_NINUNO_PERCENTAGE_PER_PARENT * 2) / 100) * breedingCockFee

    const amountToNinuno =
      (SABONG_CONFIG.BREEDING_NINUNO_PERCENTAGE_PER_PARENT / 100) * breedingCockFee * 2

    const totalFeathers = getParent1BreedingCost.feathers + getParent2BreedingCost.feathers

    const featherData = [[1, totalFeathers]] // [[tokenId, amount], ...]
    const resourcesData = [] as number[][] // [[index, tokenId, amount], ...]

    const breedingCooldownTime = totalBreedingCooldown * 24 * 60 * 60 // days to seconds

    console.log('Amount Summary:', {
      breedingCockFee,
      totalAmount,
      amountToVault,
      amountToNinuno,
      featherData,
      resourcesData,
      breedingCooldownTime,
    })

    const charHash =
      keccak256(
        encodePacked(
          [
            'address', //sender
            'uint256', //chickenLeftTokenId
            'uint256', //chickenRightTokenId
            'uint256', //totalAmount
            'uint256', //amountToVault
            'uint256', //amountToNinuno
            'uint256', //breedingTime
            'bytes32', //feathersHash
            'bytes32', //resourcesHash
          ],
          [
            ownerWalletAddress,
            BigInt(parent1),
            BigInt(parent2),
            parseEther(`${totalAmount}`),
            parseEther(`${amountToVault}`),
            parseEther(`${amountToNinuno}`),
            BigInt(breedingCooldownTime),
            keccak256(
              encodeAbiParameters(
                [{ type: 'uint256[][]' }],
                [featherData.map((arr) => arr.map((n) => BigInt(n)))]
              )
            ),
            keccak256(
              encodeAbiParameters(
                [{ type: 'uint256[][]' }],
                [resourcesData.map((arr) => arr.map((n) => BigInt(n)))]
              )
            ),
          ]
        )
      ) || ''

    console.log('charHash: ', charHash)
    const signature = await signMessage({
      message: { raw: charHash },
      privateKey: SABONG_CONFIG.SIGNER_KEY,
    })

    console.log('signature: ', signature)

    // CONTRACT INTERACTION (FRONTEND)
    const account = privateKeyToAccount(Env.get('DUMMY_ACCOUNT_KEY'))

    // APPROVED COCK
    const approveCock = await cockContract.write.approve(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, parseEther(`${totalAmount}`)],
      {
        account,
      }
    )

    console.log('approveCock: ', approveCock)

    // APPROVED FEATHERS
    const approveFeather = await itemContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )

    console.log('approveFeather: ', approveFeather)

    // APPROVED RESOURCES
    const approveResources = await resourcesContract.write.setApprovalForAll(
      [SABONG_CONFIG.CONTRACTS.BREEDING_ADDRESS, true],
      { account }
    )
    console.log('approveResources: ', approveResources)

    const breed = await breedingContract.write.breed(
      [
        {
          chickenLeftTokenId: BigInt(parent1),
          chickenRightTokenId: BigInt(parent2),
          totalAmount: parseEther(`${totalAmount}`),
          amountToVault: parseEther(`${amountToVault}`),
          amountToNinuno: parseEther(`${amountToNinuno}`),
          breedingCooldownTime: BigInt(breedingCooldownTime),
          feathersData: featherData.map((arr) => arr.map((n) => BigInt(n))),
          resourcesData: resourcesData.map((arr) => arr.map((n) => BigInt(n))),
        },
        signature,
        '', // referral code
      ],
      { account }
    )

    console.log(breed)
  })
})
