/* eslint-disable @typescript-eslint/naming-convention */
import { createCanvas, loadImage } from 'canvas'
import fs from 'fs'
import { keccak256 } from 'viem'
import {
  background,
  baseUri,
  buildDir,
  debugLogs,
  description,
  extraAttributes,
  extraMetadata,
  format,
  hashImages,
  layerConfigurations,
  layersDir,
  outputJPEG,
  rarityDelimiter,
  traitValueOverrides,
  useRootTraitType,
} from './config'
import axios from 'axios'
import FormData from 'form-data'
import SABONG_CONFIG from 'Config/sabong'
import { Traits } from './index'

const imageCache = new Map()
let totalCacheSize = 0
const MAX_CACHE_SIZE = 500 * 1024 * 1024

const canvas = createCanvas(format.width, format.height)
const ctxMain = canvas.getContext('2d')

const dependencies = {}

let legendaryCount = 0

ctxMain.imageSmoothingEnabled = format.smoothing

let metadataList = [] as any[]
let attributesList = [] as any[]

let uniqueDNAList = new Set() // internal: post-filtered dna set for bypassDNA etc.
const DNA_DELIMITER = '*'

const zflag = /(z-?\d*,)/

const buildSetup = () => {
  if (!fs.existsSync(buildDir)) {
    fs.mkdirSync(buildDir)
  }
  if (!fs.existsSync(`${buildDir}/json`)) {
    fs.mkdirSync(`${buildDir}/json`)
  }
  if (!fs.existsSync(`${buildDir}/images`)) {
    fs.mkdirSync(`${buildDir}/images`)
  }
}

const getRarityWeight = (_path: string) => {
  // check if there is an extension, if not, consider it a directory
  const exp = new RegExp(`${rarityDelimiter}(\\d*)`, 'g')
  const weight = exp.exec(_path)
  const weightNumber = weight ? Number(weight[1]) : -1

  if (weightNumber < 0 || isNaN(weightNumber)) {
    return 'required'
  }
  return weightNumber
}

const cleanDna = (_str) => {
  var dna = _str.split(':').shift()
  return dna
}

const cleanName = (_str) => {
  const zRemoved = _str.replace(zflag, '')

  const extension = /\.[0-9a-zA-Z]+$/
  const hasExtension = extension.test(zRemoved)
  let nameWithoutExtension = hasExtension ? zRemoved.slice(0, -4) : zRemoved
  var nameWithoutWeight = nameWithoutExtension.split(rarityDelimiter).shift()
  return nameWithoutWeight
}

const parseQueryString = (filename, layer, sublayer) => {
  const query = /\?(.*)\./
  const querystring = query.exec(filename)
  if (!querystring) {
    return getElementOptions(layer, sublayer)
  }

  const layerstyles = querystring[1].split('&').reduce(
    (r, setting) => {
      const keyPairs = setting.split('=')
      return { ...r, [keyPairs[0]]: keyPairs[1] }
    },
    {} as Record<string, any>
  )

  return {
    blendmode: layerstyles.blend ? layerstyles.blend : getElementOptions(layer, sublayer).blendmode,
    opacity: layerstyles.opacity
      ? layerstyles.opacity / 100
      : getElementOptions(layer, sublayer).opacity,
  }
}

/**
 * Given some input, creates a sha256 hash.
 * @param {Object} input
 */
const hash = (input) => {
  const hashable = typeof input === 'string' ? JSON.stringify(input) : input
  return keccak256(hashable, 'hex')
}

/**
 * Get't the layer options from the parent, or grandparent layer if
 * defined, otherwise, sets default options.
 *
 * @param {Object} layer the parent layer object
 * @param {String} sublayer Clean name of the current layer
 * @returns {blendmode, opacity} options object
 */
const getElementOptions = (layer, sublayer) => {
  let blendmode = 'source-over'
  let opacity = 1

  if (layer.sublayerOptions?.[sublayer]) {
    const options = layer.sublayerOptions[sublayer]

    options.blend !== undefined ? (blendmode = options.blend) : null
    options.opacity !== undefined ? (opacity = options.opacity) : null
  } else {
    // inherit parent blend mode
    blendmode = layer.blend !== undefined ? layer.blend : 'source-over'
    opacity = layer.opacity !== undefined ? layer.opacity : 1
  }
  return { blendmode, opacity }
}

const parseZIndex = (str) => {
  const z = zflag.exec(str)
  const matches = z?.[0].match(/-?\d+/)
  return matches ? parseInt(matches[0]) : null
}

const getElements = (path, layer) => {
  return fs
    .readdirSync(path)
    .filter((item) => {
      const invalid = /(\.ini)/g
      return !/(^|\/)\.[^\/\.]/g.test(item) && !invalid.test(item)
    })
    .map((i, index) => {
      const name = cleanName(i)
      const extension = /\.[0-9a-zA-Z]+$/
      const sublayer = !extension.test(i)
      const weight = getRarityWeight(i)

      const { blendmode, opacity } = parseQueryString(i, layer, name)
      //pass along the zflag to any children
      const zindexMatch = zflag.exec(i)
      const zindex = zindexMatch ? zindexMatch[0] : layer.zindex ? layer.zindex : ''

      const element = {
        sublayer,
        weight,
        blendmode,
        opacity,
        id: index,
        name,
        filename: i,
        path: `${path}${i}`,
        zindex,
      }

      if (sublayer) {
        element.path = `${path}${i}`
        const subPath = `${path}${i}/`
        const sublayer = { ...layer, blend: blendmode, opacity, zindex }
        element['elements'] = getElements(subPath, sublayer)
      }

      // Set trait type on layers for metadata
      const lineage = path.split('/')
      let typeAncestor

      if (weight !== 'required') {
        typeAncestor = element.sublayer ? 3 : 2
      }
      if (weight === 'required') {
        typeAncestor = element.sublayer ? 1 : 3
      }
      // we need to check if the parent is required, or if it's a prop-folder
      if (useRootTraitType && lineage[lineage.length - typeAncestor].includes(rarityDelimiter)) {
        typeAncestor += 1
      }

      const parentName = cleanName(lineage[lineage.length - typeAncestor])

      element['trait'] = layer.sublayerOptions?.[parentName]
        ? layer.sublayerOptions[parentName].trait
        : layer.trait !== undefined
        ? layer.trait
        : parentName

      const rawTrait = getTraitValueFromPath(element, lineage)
      const trait = processTraitOverrides(rawTrait)
      element['traitValue'] = trait

      return element
    })
}

const getTraitValueFromPath = (element, lineage) => {
  // If the element is a required png. then, the trait property = the parent path
  // if the element is a non-required png. black%50.png, then element.name is the value and the parent Dir is the prop
  if (element.weight !== 'required') {
    return element.name
  } else if (element.weight === 'required') {
    // Special handling for Body trait - use second level folder name
    if (element.trait === 'Body') {
      return cleanName(lineage[lineage.length - 3]) // Use second level folder name
    }
    // Default behavior for other traits
    return element.sublayer ? true : cleanName(lineage[lineage.length - 2])
  }
}

/**
 * Checks the override object for trait overrides
 * @param {String} trait The default trait value from the path-name
 * @returns String trait of either overridden value of raw default.
 */
const processTraitOverrides = (trait) => {
  return traitValueOverrides[trait] ? traitValueOverrides[trait] : trait
}

const layersSetup = (layersOrder) => {
  const layers = layersOrder.map((layerObj, index) => {
    return {
      id: index,
      name: layerObj.name,
      blendmode: layerObj['blend'] !== undefined ? layerObj['blend'] : 'source-over',
      opacity: layerObj['opacity'] !== undefined ? layerObj['opacity'] : 1,
      elements: getElements(`${layersDir}/${layerObj.name}/`, layerObj),
      ...(layerObj.display_type !== undefined && {
        display_type: layerObj.display_type,
      }),
      bypassDNA:
        layerObj.options?.['bypassDNA'] !== undefined ? layerObj.options?.['bypassDNA'] : false,
    }
  })

  return layers
}

const genColor = () => {
  let hue = Math.floor(Math.random() * 360)
  let pastel = `hsl(${hue}, 100%, ${background.brightness})`
  // store the background color in the dna
  // generatedBackground = pastel //TODO: storing in a global var is brittle. could be improved.
  return pastel
}

const drawBackground = (canvasContext, background) => {
  canvasContext.fillStyle = background.HSL ?? genColor()

  canvasContext.fillRect(0, 0, format.width, format.height)
}

const selectRandomColor = () => {
  const colorArray = [
    'A1',
    'A2',
    'A3',
    'B1',
    'B2',
    'B3',
    'C1',
    'C2',
    'C3',
    'D1',
    'D2',
    'D3',
    'E1',
    'E2',
    'E3',
    'F1',
    'F2',
    'F3',
    'G1',
    'G2',
    'G3',
    'H1',
    'H2',
    'H3',
  ]

  const randomIndex = Math.floor(Math.random() * colorArray.length)
  return colorArray[randomIndex]
}

// Trait mappings with legendary traits marked
const traitMappings = {
  Beak: [
    'Chim Lạc',
    'Thunderbird',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
    'Wormtongue',
    'Raven',
    'Ironclad',
    'Piercing Fang',
    'Haki',
    'Nightwave',
    'Touca',
    'Blade Spire',
    'Aurora',
    'Verdant',
    'Greenbill',
    'Bluelip',
    'Radiant',
    'Flare',
    'Ashfire',
    'Boneblade',
  ],
  Body: ['Hoeltaf', 'Chibidei', 'Draken', 'Emilia', 'Badwitch', 'Chummiest', 'Wickid', 'Jordi'],
  Comb: [
    'Minokawa',
    'Adarna',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
    'Suave',
    'Single',
    'Corona',
    'Goodboy',
    'Sasuke',
    'Cubao',
    'Igop',
    'Hellboy',
    'Spike',
    'Raditz',
    'Super Sayang 4',
    'Power Geyser',
    'Yugi',
    'Super Sayang 1',
    'Killua',
    'Sakuragi',
  ],
  Eyes: [
    'Garuda',
    'Minokawa',
    'Adarna',
    'Sarimanok', // Legendary traits (0-3)
    'Batak',
    'Bagyo',
    'Shookt',
    'Dyosa',
    'Silog',
    'Santelmo',
    'Tuko',
    'Peyups',
    'Agila',
    'Atenista',
    'Lasallista',
    'Wildfire',
    'Diwata',
    'Maxx',
    'Retokada',
    'Yinyang',
  ],
  Feet: [
    'Buakaw',
    'Alicanto Oro',
    'Alicanto Plata',
    'Thunderbird', // Legendary traits (0-3)
    'Mahiwaga',
    'Luntian',
    'Makopa',
    'Sibat',
    'Cemani',
    'Pula',
    'Zenki',
    'Ember',
    'Hepa Lane',
    'Kaliskis',
    'Mewling Tiger',
    'Dionela',
    'Onyx',
    'Chernobyl',
    'Paleclaws',
    'Catriona',
  ],
  Tail: [
    'Simurgh',
    'Chim Lạc',
    'Minokawa',
    'Adarna', // Legendary traits (0-3)
    'Agave',
    'Rengoku',
    'Starjeatl',
    'Carota',
    'Abaniko',
    'Onagadori',
  ],
  Wings: [
    'Adarna',
    'Minokawa',
    'Garuda',
    'Simurgh', // Legendary traits (0-3)
    'Mandingo',
    'Helena',
    'Potsu',
    'Johny',
    'Slenduh',
    'Awra',
  ],
  Color: [
    'A1',
    'A2',
    'A3',
    'B1',
    'B2',
    'B3',
    'C1',
    'C2',
    'C3',
    'D1',
    'D2',
    'D3',
    'E1',
    'E2',
    'E3',
    'F1',
    'F2',
    'F3',
    'G1',
    'G2',
    'G3',
    'H1',
    'H2',
    'H3',
  ],
  Instinct: [
    'Aggressive',
    'Steadfast',
    'Swift',
    'Stalwart',
    'Balanced',
    'Reckless',
    'Resolute',
    'Elusive',
    'Tenacious',
    'Unyielding',
    'Vicious',
    'Adaptive',
    'Versatile',
    'Relentless',
    'Blazing',
    'Bulwark',
    'Enduring',
  ],
}

// Function to get a random non-legendary trait index
function getRandomNonLegendaryIndex(traitName) {
  const traitList = traitMappings[traitName]
  const legendaryCount = traitName === 'Body' || traitName === 'Color' ? 0 : 4 // Body has no legendary traits

  // Return a random index from the non-legendary range
  return legendaryCount + Math.floor(Math.random() * (traitList.length - legendaryCount))
}

function getRandomNonLegendaryTrait(traitName) {
  const traitList = traitMappings[traitName]
  const legendaryCount = traitName === 'Body' || traitName === 'Color' ? 0 : 4 // Body has no legendary traits

  // Return a random trait from the non-legendary range
  return traitMappings[traitName][
    legendaryCount + Math.floor(Math.random() * (traitList.length - legendaryCount))
  ]
}

// SIMPLIFIED: Function to encode a trait into a gene segment
function encodeTraitToGene(traitName, traitValue, isComposite, offspringTraits) {
  if (isComposite) {
    // Check if traitName is a valid composite trait
    if (
      traitName in traitMappings &&
      ['Beak', 'Body', 'Comb', 'Eyes', 'Feet', 'Tail', 'Wings', 'Color'].includes(traitName)
    ) {
      const traitList = traitMappings[traitName]
      const p = traitList.indexOf(traitValue)

      if (p === -1) {
        console.warn(
          `Warning: Trait value "${traitValue}" not found in ${traitName} list. Using default index 0.`
        )
        return 0
      }

      // Generate random recessive traits (never legendary, but can be the same)
      let h1
      let h2
      let h3

      // For h1, h2, h3 - always use non-legendary traits
      if (offspringTraits && offspringTraits[traitName]) {
        h1 = traitList.indexOf(offspringTraits[traitName].h1)
        h2 = traitList.indexOf(offspringTraits[traitName].h2)
        h3 = traitList.indexOf(offspringTraits[traitName].h3)
      } else {
        h1 = getRandomNonLegendaryIndex(traitName)
        h2 = getRandomNonLegendaryIndex(traitName)
        h3 = getRandomNonLegendaryIndex(traitName)
      }

      // SIMPLIFIED ENCODING: Just store the indices directly
      // Format: p (8 bits) + h1 (8 bits) + h2 (8 bits) + h3 (8 bits) = 32 bits
      // We'll only use the lower 8 bits of each 16-bit segment
      return (p << 24) | (h1 << 16) | (h2 << 8) | h3
    }
    return 0 // Default value for unknown traits
  } else {
    // For simple traits, just store the index
    if (traitName === 'Instinct' && traitName in traitMappings) {
      const instinctIndex = traitMappings.Instinct.indexOf(traitValue)
      return instinctIndex >= 0 ? instinctIndex : 0
    } else if (
      traitName.includes('Attack') ||
      traitName.includes('Defense') ||
      traitName.includes('Speed') ||
      traitName.includes('Health')
    ) {
      // For numeric stats, just use the value directly
      const numValue = parseInt(traitValue)
      return isNaN(numValue) ? 0 : numValue
    }
  }
  return 0
}

// Function to generate a gene from traits
function generateBaseGene(traits, offspringTraits) {
  const traitTemplate = [
    { trait: 'Feet', isComposite: true },
    { trait: 'Tail', isComposite: true },
    { trait: 'Body', isComposite: true },
    { trait: 'Wings', isComposite: true },
    { trait: 'Eyes', isComposite: true },
    { trait: 'Beak', isComposite: true },
    { trait: 'Comb', isComposite: true },
    { trait: 'Color', isComposite: true },
    { trait: 'Innate Attack', isComposite: false },
    { trait: 'Innate Defense', isComposite: false },
    { trait: 'Innate Speed', isComposite: false },
    { trait: 'Innate Health', isComposite: false },
    { trait: 'Instinct', isComposite: false },
  ]

  // Generate segments for each trait
  const geneSegments = [] as number[]

  for (const template of traitTemplate) {
    const traitName = template.trait
    const traitValue = traits[traitName]

    if (traitValue !== undefined) {
      const geneSegment = encodeTraitToGene(
        traitName,
        String(traitValue),
        template.isComposite,
        offspringTraits
      )
      geneSegments.push(geneSegment)
    } else {
      // If trait is missing, use a placeholder
      geneSegments.push(0)
    }
  }

  // Fill remaining segments with zeros to make 16 segments total
  while (geneSegments.length < 16) {
    geneSegments.push(0)
  }

  // Convert segments to binary, then to hex
  let binaryString = ''
  for (const segment of geneSegments) {
    // Convert each segment to a 32-bit binary string
    binaryString += segment.toString(2).padStart(32, '0')
  }

  // Use BigInt for large binary numbers
  const geneHex = BigInt('0b' + binaryString)
    .toString(16)
    .padStart(64, '0')
  return geneHex
}

const addMetadata = (
  _dna,
  _edition,
  _prefixData,
  chickenLeftTokenId,
  chickenRightTokenId,
  generation,
  offspringTraits,
  offspringInstinct,
  offspringInnatePoints
) => {
  let dateTime = Math.floor(Date.now() / 1000)
  const { _prefix, _imageHash } = _prefixData

  const updatedAttributesList = attributesList.map((item) => ({
    ...item,
    display_type: 'string',
  }))

  const combinedAttrs = [...updatedAttributesList, ...extraAttributes(), ...offspringInnatePoints]
  !debugLogs ? null : console.log('@@@ attr: ', updatedAttributesList)

  const cleanedAttrs = combinedAttrs.reduce((acc, current) => {
    const x = acc.find((item) => item.trait_type === current.trait_type)
    if (!x) {
      return acc.concat([current])
    } else {
      return acc
    }
  }, [])

  // Add the Instinct trait
  cleanedAttrs.push({
    trait_type: 'Instinct',
    value: offspringInstinct,
    display_type: 'string',
  })

  cleanedAttrs.push({
    trait_type: 'Birthdate',
    value: dateTime,
    display_type: 'date',
  })

  cleanedAttrs.push({
    trait_type: 'Legendary Count',
    value: legendaryCount,
    display_type: 'number',
  })

  let dailyFeathers = {
    trait_type: 'Daily Feathers',
    value: 0, //normal chickens get 0 feathers per day
    display_type: 'number',
  }

  cleanedAttrs.push({
    trait_type: 'Parent 1',
    value: Number(chickenLeftTokenId),
    display_type: 'number',
  })

  cleanedAttrs.push({
    trait_type: 'Parent 2',
    value: Number(chickenRightTokenId),
    display_type: 'number',
  })

  cleanedAttrs.push({
    trait_type: 'Generation',
    value: generation,
    display_type: 'string',
  })

  cleanedAttrs.push(dailyFeathers)

  const bodyColor = {
    trait_type: 'Color',
    value: dependencies['Color'],
    display_type: 'string',
  }

  cleanedAttrs.push(bodyColor)

  !debugLogs ? null : console.log('@@@ cleanedAttrs:', cleanedAttrs)

  cleanedAttrs.shift() // removes background

  // Extract traits
  const traitsObj = {}
  if (cleanedAttrs && Array.isArray(cleanedAttrs)) {
    for (const attr of cleanedAttrs) {
      if (attr.trait_type) {
        traitsObj[attr.trait_type] = attr.value
      }
    }
  }

  !debugLogs ? null : console.log('@@@ traitsObj:', traitsObj)

  cleanedAttrs.push({
    trait_type: 'Genes',
    value: generateBaseGene(traitsObj, offspringTraits),
    display_type: 'string',
  })

  !debugLogs ? null : console.log(generateBaseGene(traitsObj, offspringTraits))

  let tempMetadata = {
    name: `${_prefix ? _prefix + ' ' : ''}#${_edition}`,
    nickname: '',
    description: description,
    image: `${baseUri}/${_edition}${outputJPEG ? '.jpg' : '.png'}`,
    ...(hashImages === true && { imageHash: _imageHash }),
    edition: _edition,
    ...extraMetadata,
    attributes: cleanedAttrs,
  }

  !debugLogs ? null : console.log('@@@ legendary count:', legendaryCount)

  metadataList.push(tempMetadata)
  legendaryCount = 0
  attributesList = []

  return tempMetadata
}

const addAttributes = (_element) => {
  let selectedElement = _element.layer
  const layerAttributes = {
    trait_type: _element.layer.trait,
    value: selectedElement.traitValue,
    ...(_element.layer.display_type !== undefined && {
      display_type: _element.layer.display_type,
    }),
  }
  if (attributesList.some((attr) => attr.trait_type === layerAttributes.trait_type)) return
  attributesList.push(layerAttributes)
}

const loadLayerImg = async (_layer) => {
  return new Promise(async (resolve) => {
    // Check cache first
    if (imageCache.has(_layer.path)) {
      resolve({ layer: _layer, loadedImage: imageCache.get(_layer.path) })
      return
    }

    // Load if not cached
    const image = await loadImage(`${_layer.path}`).catch((err) =>
      !debugLogs ? null : console.log(`failed to load ${_layer.path}`, err)
    )

    if (!image) {
      console.error(`Failed to load image: ${_layer.path}`)
      resolve({ layer: _layer, loadedImage: null })
      return
    }

    const estimatedSize = image.width * image.height * 4

    // Check if adding this image would exceed our cache limit
    if (totalCacheSize + estimatedSize > MAX_CACHE_SIZE) {
      // Clear part of the cache to make room
      const entriesToRemove = [] as any[]
      let sizeToFree = estimatedSize

      for (const [key, cachedImage] of imageCache.entries()) {
        const imageSize = cachedImage.width * cachedImage.height * 4
        entriesToRemove.push(key)
        sizeToFree -= imageSize
        totalCacheSize -= imageSize

        if (sizeToFree <= 0) break
      }

      // Remove the selected entries
      entriesToRemove.forEach((key) => imageCache.delete(key))
    }

    // Store in cache
    imageCache.set(_layer.path, image)
    totalCacheSize += estimatedSize

    resolve({ layer: _layer, loadedImage: image })
  })
}

const drawElement = (_renderObject) => {
  const layerCanvas = createCanvas(format.width, format.height)
  const layerctx = layerCanvas.getContext('2d')
  layerctx.imageSmoothingEnabled = format.smoothing

  layerctx.drawImage(_renderObject.loadedImage, 0, 0, format.width, format.height)

  addAttributes(_renderObject)
  return layerCanvas
}

const constructLayerToDna = (_dna, _layers) => {
  const dna = _dna.split(DNA_DELIMITER)
  let mappedDnaToLayers = _layers.map((layer) => {
    let selectedElements = [] as any[]
    const layerImages = dna.filter((element) => `${element.split('.')[0]}` === `${layer.id}`)
    layerImages.forEach((img) => {
      const indexAddress = cleanDna(img)

      const indices = indexAddress.toString().split('.')
      // const firstAddress = indices.shift();
      const lastAddress = indices.pop() // 1
      // recursively go through each index to get the nested item
      let parentElement = indices.reduce((r, nestedIndex) => {
        if (!r[nestedIndex]) {
          throw new Error('wtf')
        }
        return r[nestedIndex].elements
      }, _layers) //returns string, need to return

      selectedElements.push(parentElement[lastAddress])
    })
    // If there is more than one item whose root address indicies match the layer ID,
    // continue to loop through them an return an array of selectedElements

    return {
      name: layer.name,
      blendmode: layer.blendmode,
      opacity: layer.opacity,
      selectedElements: selectedElements,
      ...(layer.display_type !== undefined && {
        display_type: layer.display_type,
      }),
    }
  })
  return mappedDnaToLayers
}

/**
 * In some cases a DNA string may contain optional query parameters for options
 * such as bypassing the DNA isUnique check, this function filters out those
 * items without modifying the stored DNA.
 *
 * @param {String} _dna New DNA string
 * @returns new DNA string with any items that should be filtered, removed.
 */
const filterDNAOptions = (_dna) => {
  const filteredDNA = _dna.split(DNA_DELIMITER).filter((element) => {
    const query = /(\?.*$)/
    const querystring = query.exec(element)
    if (!querystring) {
      return true
    }
    // convert the items in the query string to an object
    const options = querystring[1].split('&').reduce(
      (r, setting) => {
        const keyPairs = setting.split('=')
        //   construct the object →       {bypassDNA: bool}
        return { ...r, [keyPairs[0].replace('?', '')]: keyPairs[1] }
      },
      {} as Record<string, any>
    )
    // currently, there is only support for the bypassDNA option,
    // when bypassDNA is true, return false to omit from .filter
    return options.bypassDNA === 'true' ? false : true
  })

  return filteredDNA.join(DNA_DELIMITER)
}

/**
 * determine if the sanitized/filtered DNA string is unique or not by comparing
 * it to the set of all previously generated permutations.
 *
 * @param {String} _dna string
 * @returns isUnique is true if uniqueDNAList does NOT contain a match,
 *  false if uniqueDANList.has() is true
 */
const isDnaUnique = (_dna = []) => {
  const filtered = filterDNAOptions(_dna)
  return !uniqueDNAList.has(filtered)
}

// expecting to return an array of strings for each _layer_ that is picked,
// should be a flattened list of all things that are picked randomly AND required
/**
 * Generates a DNA sequence for a layer and its sublayers, handling compatibility, forced selections, and dependencies
 *
 * @param {Object} layer - The main layer configuration object defined in config.layerConfigurations
 * @param {string[]} dnaSequence - Array of DNA strings representing layer mappings and nesting structure
 * @param {string} parentId - Nested parent ID used for recursive calls with sublayers
 * @param {string[]} incompatibleDNA - Array of layer names that are incompatible with current selection
 * @param {string[]} forcedDNA - Array of layer names that must be selected
 * @param {string} bypassDNA - Query string parameter for bypassing DNA uniqueness check
 * @param {number} zIndex - Defines layer stacking order from top down
 * @param {Record<string, string>} dependencies - Object tracking dependencies between layers (e.g., color matching)
 * @param {boolean} forMatching - Flag indicating if color matching should be applied
 * @returns {string[]} Array of DNA sequence strings
 */
function pickRandomElement(
  layer,
  dnaSequence,
  parentId,
  incompatibleDNA,
  forcedDNA,
  bypassDNA,
  zIndex,
  dependencies,
  forMatching
) {
  let totalWeight = 0
  !debugLogs ? null : console.log('@@@ dependencies: ', dependencies)

  const compatibleLayers = layer.elements.filter((layer) => !incompatibleDNA.includes(layer.name))

  if (compatibleLayers.length === 0) {
    return dnaSequence
  }

  // Add dependency logic for Wings, Tail and Body matching color
  !debugLogs ? null : console.log('@@@ dependencies[Body]', dependencies['Body'])
  !debugLogs ? null : console.log('@@@ layer', layer)
  !debugLogs ? null : console.log('@@@ layer name', layer.name)
  if (forMatching) {
    !debugLogs ? null : console.log('@@@ pasok here', compatibleLayers)

    const matching = compatibleLayers.find((element) => element.name === dependencies['Color'])
    if (matching) {
      if (matching.sublayer) {
        !debugLogs ? null : console.log('>>>>>>>> MATCHING:', matching)
        return dnaSequence.concat(
          pickRandomElement(
            matching,
            dnaSequence,
            `${parentId}.${matching.id}`,
            incompatibleDNA,
            forcedDNA,
            bypassDNA,
            zIndex,
            dependencies,
            forMatching
          )
        )
      }

      let dnaString = `${parentId}.${matching.id}:${matching.zindex}${matching.filename}${bypassDNA}`
      dnaSequence.push(dnaString)
      return dnaSequence
    }
  }

  const forcedPick = layer.elements.find((element) => forcedDNA.includes(element.name))
  if (forcedPick) {
    if (forcedPick.sublayer) {
      if (layer.name === 'Tail' || layer.name === 'Wings' || layer.name === 'Body') {
        forMatching = true
      } else {
        forMatching = false
      }

      return dnaSequence.concat(
        pickRandomElement(
          forcedPick,
          dnaSequence,
          `${parentId}.${forcedPick.id}`,
          incompatibleDNA,
          [],
          bypassDNA,
          zIndex,
          dependencies,
          forMatching
        )
      )
    }
    let dnaString = `${parentId}.${forcedPick.id}:${forcedPick.zindex}${forcedPick.filename}${bypassDNA}`
    dnaSequence.push(dnaString)
    return dnaSequence
  } else {
    // Random selection logic for other layers
    compatibleLayers.forEach((element) => {
      if (element.weight === 'required' && !element.sublayer) {
        let dnaString = `${parentId}.${element.id}:${element.zindex}${element.filename}${bypassDNA}`
        !debugLogs ? null : console.log('@@@ dnaString', dnaString)
        dnaSequence.unshift(dnaString)
        return
      }
      if (element.weight === 'required' && element.sublayer) {
        !debugLogs ? null : console.log('@@@ dnaStringWithSublayer', element.sublayer)
        pickRandomElement(
          element,
          dnaSequence,
          `${parentId}.${element.id}`,
          incompatibleDNA,
          forcedDNA,
          bypassDNA,
          zIndex,
          dependencies,
          forMatching
        )
      }
      if (element.weight !== 'required') {
        totalWeight += element.weight
      }
    })

    let random = Math.floor(Math.random() * totalWeight)

    for (let i = 0; i < compatibleLayers.length; i++) {
      random -= compatibleLayers[i].weight

      if (random < 0) {
        !debugLogs ? null : console.log('@@@ id:', compatibleLayers[i].id)
        !debugLogs ? null : console.log('@@@ layer:', compatibleLayers[i].name)

        if (compatibleLayers[i].weight === 1) {
          legendaryCount++
        }
        if (compatibleLayers[i].sublayer) {
          !debugLogs ? null : console.log('pasok')
          !debugLogs ? null : console.log('@@@ compatible sublayer:', compatibleLayers[i].name)

          if (layer.name === 'Tail' || layer.name === 'Wings' || layer.name === 'Body') {
            forMatching = true
          } else {
            forMatching = false
          }

          return dnaSequence.concat(
            pickRandomElement(
              compatibleLayers[i],
              dnaSequence,
              `${parentId}.${compatibleLayers[i].id}`,
              incompatibleDNA,
              forcedDNA,
              bypassDNA,
              zIndex,
              dependencies,
              forMatching
            )
          )
        }

        let dnaString = `${parentId}.${compatibleLayers[i].id}:${compatibleLayers[i].zindex}${compatibleLayers[i].filename}${bypassDNA}`
        dnaSequence.push(dnaString)
        return dnaSequence
      }
    }
  }
}

/**
 * given the nesting structure is complicated and messy, the most reliable way to sort
 * is based on the number of nested indecies.
 * This sorts layers stacking the most deeply nested grandchildren above their
 * immediate ancestors
 * @param {[String]} layers array of dna string sequences
 */
const sortLayers = (layers) => {
  const nestedsort = layers.sort((a, b) => {
    const addressA = a.split(':')[0]
    const addressB = b.split(':')[0]
    return addressA.length - addressB.length
  })

  let stack = { front: [], normal: [], end: [] }
  stack = nestedsort.reduce((acc, layer) => {
    const zindex = parseZIndex(layer)
    if (!zindex) return { ...acc, normal: [...(acc.normal ? acc.normal : []), layer] }
    // move negative z into `front`
    if (zindex < 0) return { ...acc, front: [...(acc.front ? acc.front : []), layer] }
    // move positive z into `end`
    if (zindex > 0) return { ...acc, end: [...(acc.end ? acc.end : []), layer] }
    // make sure front and end are sorted
    // contat everything back to an ordered array
  }, stack)

  // sort the normal array
  stack.normal.sort()

  return sortByZ(stack.front).concat(stack.normal).concat(sortByZ(stack.end))
}

/** File String sort by zFlag */
function sortByZ(dnastrings) {
  return dnastrings.sort((a, b) => {
    const indexA = parseZIndex(a)
    const indexB = parseZIndex(b)
    return indexA! - indexB!
  })
}

/**
 * Sorting by index based on the layer.z property
 * @param {Array } layers selected Image layer objects array
 */
function sortZIndex(layers) {
  return layers.sort((a, b) => {
    const indexA = parseZIndex(a.zindex)
    const indexB = parseZIndex(b.zindex)
    return indexA! - indexB!
  })
}

/**
 * Paints the given renderOjects to the main canvas context.
 *
 * @param {Array} renderObjectArray Array of render elements to draw to canvas
 * @param {Object} layerData data passed from the current iteration of the loop or configured dna-set
 *
 */
const paintLayers = (canvasContext, renderObjectArray, layerData) => {
  !debugLogs ? null : console.log('\nClearing canvas')
  !debugLogs ? null : console.log('@@@ renderObjectArray: ', renderObjectArray)
  canvasContext.clearRect(0, 0, format.width, format.height)

  const { _background } = layerData

  renderObjectArray.forEach((renderObject) => {
    // one main canvas
    // each render Object should be a solo canvas
    // append them all to main canbas
    canvasContext.globalAlpha = renderObject.layer.opacity
    canvasContext.globalCompositeOperation = renderObject.layer.blendmode
    canvasContext.drawImage(drawElement(renderObject), 0, 0, format.width, format.height)
  })

  if (_background.generate) {
    canvasContext.globalCompositeOperation = 'destination-over'
    drawBackground(canvasContext, background)
  }
}

const postProcessMetadata = (tokenId, layerData) => {
  const { layerConfigIndex } = layerData
  // Metadata options
  const savedFile = fs.readFileSync(`${buildDir}/images/${tokenId}${outputJPEG ? '.jpg' : '.png'}`)
  const _imageHash = hash(savedFile)

  // if there's a prefix for the current configIndex, then
  // start count back at 1 for the name, only.
  const _prefix = layerConfigurations[layerConfigIndex].namePrefix
    ? layerConfigurations[layerConfigIndex].namePrefix
    : null

  return {
    _imageHash,
    _prefix,
  }
}

const outputFiles = (
  generation,
  chickenLeftTokenId,
  chickenRightTokenId,
  tokenId,
  layerData,
  offspringTraits,
  offspringInstinct,
  offspringInnatePoints,
  _buildDir = buildDir,
  _canvas = canvas
) => {
  const { newDna } = layerData

  // Use more efficient compression settings
  const buffer = _canvas.toBuffer(
    //@ts-ignore
    outputJPEG ? 'image/jpeg' : 'image/png',
    outputJPEG ? { quality: 0.9 } : { compressionLevel: 6 }
  )

  fs.writeFileSync(`${_buildDir}/images/${tokenId}${outputJPEG ? '.jpg' : '.png'}`, buffer)

  const { _imageHash, _prefix } = postProcessMetadata(tokenId, layerData)

  const metadata = addMetadata(
    newDna,
    tokenId,
    {
      _prefix,
      _imageHash,
    },
    chickenLeftTokenId,
    chickenRightTokenId,
    generation,
    offspringTraits,
    offspringInstinct,
    offspringInnatePoints
  )

  let eggMetadata = {
    name: 'Egg',
    description: '',
    image: `${baseUri}/${tokenId}${outputJPEG ? '.jpg' : '.png'}`,
    attributes: [
      {
        trait_type: 'Type',
        value: 'Egg',
        display_type: 'string',
      },
      {
        trait_type: 'Parent 1',
        value: Number(chickenLeftTokenId),
        display_type: 'number',
      },
      {
        trait_type: 'Parent 2',
        value: Number(chickenRightTokenId),
        display_type: 'number',
      },
    ],
  }

  fs.writeFileSync(`${_buildDir}/json/egg.json`, JSON.stringify(eggMetadata, null, 2))
  fs.writeFileSync(`${_buildDir}/json/${tokenId}.json`, JSON.stringify(metadata, null, 2))

  !debugLogs ? null : console.log(`Created edition: ${tokenId}`)
  return metadata
}

const createForcedDna = (_layers, forcedDNA = [] as { trait: string; value: string }[]) => {
  let dnaSequence = [] as any[]
  let incompatibleDNA = [] as any[]

  // dependencies['Color'] = selectRandomColor() //make random color A1 for now
  const hasColor = forcedDNA.find((forced) => forced.trait === 'Color')

  dependencies['Color'] = hasColor ? hasColor.value : selectRandomColor() //make random color A1 for now

  _layers.forEach((layer) => {
    !debugLogs ? null : console.log('@@@ layer:', layer)
    const layerSequence = [] as any[]
    const forMatching = false
    const isMatchLayer = forcedDNA.find((forced) => forced.trait === layer.name)

    pickRandomElement(
      layer,
      layerSequence,
      layer.id,
      incompatibleDNA,
      isMatchLayer ? [isMatchLayer.value] : [],
      layer.bypassDNA ? '?bypassDNA=true' : '',
      layer.zindex ? layer.zIndex : '',
      dependencies,
      forMatching
    )

    const sortedLayers = sortLayers(layerSequence)
    dnaSequence = [...dnaSequence, [sortedLayers]]
  })

  !debugLogs ? null : console.log('@@@ DNA:', dnaSequence)

  const zSortDNA = sortByZ(dnaSequence.flat(2))
  const dnaStrand = zSortDNA.join(DNA_DELIMITER)
  !debugLogs ? null : console.log('@@@ DNA Strand:', dnaStrand)
  return dnaStrand
}

const syncMetadata = async (tokenId: number, isHatched: boolean) => {
  const jsonPath = `${buildDir}/json/${isHatched ? tokenId : 'egg'}.json`
  const imagePath = `${buildDir}/images/${isHatched ? tokenId : 'egg'}${
    outputJPEG ? '.jpg' : '.png'
  }`

  const formData = new FormData()
  formData.append('tokenId', tokenId.toString())
  formData.append('metadata', fs.createReadStream(jsonPath))
  formData.append('image', fs.createReadStream(imagePath))

  try {
    await axios.post(`${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/metadata/sync`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
        'x-api-key': SABONG_CONFIG.CHICKEN_IVORY_API_KEY,
      },
    })
  } catch (error) {
    console.error(`Failed to sync metadata of ${tokenId}:`, error)
  } finally {
    refreshMetadata(
      [tokenId.toString()],
      SABONG_CONFIG.CHICKEN_GENESIS_THRESHOLD >= tokenId
        ? SABONG_CONFIG.CONTRACTS.CHICKEN_GENESIS_ADDRESS
        : SABONG_CONFIG.CONTRACTS.CHICKEN_LEGACY_ADDRESS
    )
  }
}

const refreshMetadata = async (tokenIds: string[], collection: string) => {
  try {
    const response = await axios.post(
      `https://api-gateway.skymavis.com/mavis-market-partner/collections/${collection}/refresh_metadata`,
      {
        token_ids: tokenIds,
      },
      {
        headers: {
          'Content-Type': 'application/json',
          'X-API-Key': SABONG_CONFIG.SKYMAVIS_API_KEY,
        },
      }
    )

    return response.data
  } catch (error) {
    console.error(`Failed to refresh metadata ${tokenIds}:`, error)
  }
}

const generate = async (
  newChickenTokenId: number,
  forcedDNA = [] as { trait: string; value: string }[],
  chickenLeftTokenId: number,
  chickenRightTokenId: number,
  generation: string,
  offspringTraits: Partial<Traits>,
  offspringInstinct: string,
  offspringInnatePoints: { trait_type: string; value: number; display_type: string }[]
) => {
  let layerConfigIndex = 0

  const layers = layersSetup(layerConfigurations[layerConfigIndex].layersOrder)

  // Process one at a time to avoid conflicts with global variables
  let newDna = createForcedDna(layers, [...forcedDNA, { trait: 'Background', value: 'Normal' }])

  if (isDnaUnique(newDna)) {
    let results = constructLayerToDna(newDna, layers)
    !debugLogs ? null : console.log('@@@ results: ', results)
    !debugLogs ? null : console.log('DNA:', newDna.split(DNA_DELIMITER))
    let loadedElements = [] as any[]

    const allImages = results.reduce((images, layer) => {
      return [...images, ...layer.selectedElements]
    }, [])

    sortZIndex(allImages).forEach((layer) => {
      loadedElements.push(loadLayerImg(layer))
    })

    !debugLogs ? null : console.log('@@@ loadedElements: ', loadedElements)

    const output = await Promise.all(loadedElements).then((renderObjectArray) => {
      const layerData = {
        newDna,
        layerConfigIndex,
        _background: background,
      }
      paintLayers(ctxMain, renderObjectArray, layerData)
      const result = outputFiles(
        generation,
        chickenLeftTokenId,
        chickenRightTokenId,
        newChickenTokenId,
        layerData,
        offspringTraits,
        offspringInstinct,
        offspringInnatePoints
      )
      return result
    })

    await syncMetadata(newChickenTokenId, false)
    return output
  } else {
    !debugLogs ? null : console.log('DNA exists!')
  }
}

export { buildSetup, generate, getRandomNonLegendaryTrait, syncMetadata }
