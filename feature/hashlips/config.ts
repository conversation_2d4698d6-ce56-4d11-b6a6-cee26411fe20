/* eslint-disable @typescript-eslint/naming-convention */
import Application from '@ioc:Adonis/Core/Application'
import SABONG_CONFIG from 'Config/sabong'

const buildDir = `${Application.tmpPath()}/build`
const layersDir = `${Application.appRoot}/feature/hashlips/layers`

interface Format {
  width: number
  height: number
  smoothing: boolean
}

interface Background {
  generate: boolean
  brightness: string
}

interface LayerOptions {
  bypassDNA?: boolean
}

interface Layer {
  name: string
  options?: LayerOptions
}

interface LayerConfiguration {
  growEditionSizeTo: number
  namePrefix: string
  layersOrder: Layer[]
}

interface Preview {
  thumbPerRow: number
  thumbWidth: number
  imageRatio: number
  imageName: string
}

interface PreviewGif {
  numberOfImages: number
  order: 'ASC' | 'DESC' | 'MIXED'
  repeat: number
  quality: number
  delay: number
  imageName: string
}

interface Attribute {
  display_type: string
  trait_type: string
  value: string | number
}

const description = ''
const baseUri = `${SABONG_CONFIG.CHICKEN_IVORY_API_URL}/api/image`

const outputJPEG = false // if false, the generator outputs png's

const format: Format = {
  width: 512,
  height: 512,
  smoothing: true, // set to false when up-scaling pixel art.
}

const background: Background = {
  generate: false,
  brightness: '80%',
}

const layerConfigurations: LayerConfiguration[] = [
  {
    growEditionSizeTo: 8888,
    namePrefix: 'Chicken',
    layersOrder: [
      { name: 'Feet' },
      { name: 'Body' },
      { name: 'Tail' },
      { name: 'Wings' },
      { name: 'Beak' },
      { name: 'Eyes' },
      { name: 'Comb' },
      {
        name: 'Background',
        options: {
          bypassDNA: false,
        },
      },
    ],
  },
]

const shuffleLayerConfigurations = false
const debugLogs = false
const emptyLayerName = 'NONE'

const incompatible: Record<string, string[]> = {
  // Red: ["Dark Long"],
  // White: ["rare-Pink-Pompadour"],
}

const forcedCombinations: Record<string, string[]> = {
  // floral: ["MetallicShades", "Golden Sakura"],
}

const traitValueOverrides: Record<string, string> = {
  'Helmet': 'Space Helmet',
  'gold chain': 'GOLDEN NECKLACE',
}

const extraMetadata: Record<string, unknown> = {}

const extraAttributes = (): Attribute[] => [
  {
    trait_type: 'Grit Attack',
    value: 0,
    display_type: 'number',
  },
  {
    trait_type: 'Grit Defense',
    value: 0,
    display_type: 'number',
  },
  {
    trait_type: 'Grit Speed',
    value: 0,
    display_type: 'number',
  },
  {
    trait_type: 'Grit Health',
    value: 0,
    display_type: 'number',
  },
  {
    trait_type: 'Breed Count',
    value: 0,
    display_type: 'number',
  },
  {
    trait_type: 'Gender',
    value: 'Rooster',
    display_type: 'string',
  },
  {
    trait_type: 'Level',
    value: 1,
    display_type: 'number',
  },
  {
    trait_type: 'Type',
    value: 'Ordinary',
    display_type: 'string',
  },
]

const hashImages = true
const rarityDelimiter = '#'
const uniqueDnaTorrance = 10000
const useRootTraitType = true

const preview: Preview = {
  thumbPerRow: 5,
  thumbWidth: 50,
  imageRatio: format.width / format.height,
  imageName: 'preview.png',
}

const preview_gif: PreviewGif = {
  numberOfImages: 5,
  order: 'ASC',
  repeat: 0,
  quality: 100,
  delay: 500,
  imageName: 'preview.gif',
}

export {
  background,
  baseUri,
  buildDir,
  debugLogs,
  description,
  emptyLayerName,
  extraAttributes,
  extraMetadata,
  forcedCombinations,
  format,
  hashImages,
  incompatible,
  layerConfigurations,
  layersDir,
  outputJPEG,
  preview,
  preview_gif,
  rarityDelimiter,
  shuffleLayerConfigurations,
  traitValueOverrides,
  uniqueDnaTorrance,
  useRootTraitType,
}
